﻿using Azure.Core;
using BIDConfirmation.Model;
using BIDConfirmation.Models;
using BIDConfirmation.Services;
using BidConfirmationAPI.Data;
using BidConfirmationAPI.Interfaces;
using BidConfirmationAPI.Model;
using BidConfirmationAPI.Models;
using ClosedXML.Excel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using RestSharp.Extensions;
using System.DirectoryServices.Protocols;
using System.Net;

namespace BidConfirmationAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UserAccountController : ControllerBase
    {
        private readonly IUserAccountService _userAccountService;
        private readonly ILogger<UserAccountController> _logger;
        private readonly ApplicationDbContext _context;
        private readonly EmailService _email;
        private readonly IWebHostEnvironment _env;
        private readonly IConfiguration _config;


        public UserAccountController(
            IUserAccountService userAccountService,
            ILogger<UserAccountController> logger, ApplicationDbContext context, IWebHostEnvironment env, EmailService email, IConfiguration config)
        {
            _userAccountService = userAccountService ?? throw new ArgumentNullException(nameof(userAccountService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _context  = context;
            _env = env;
            _email = email;
            _config = config;
        }

        [Authorize(Policy = "RequireRMRole")]
        [HttpGet("details/{accountNo}")]
        public async Task<IActionResult> GetDetailsByAccountNo(string accountNo)
        {
            if (string.IsNullOrWhiteSpace(accountNo))
            {
                _logger.LogError($"Account number provided: {accountNo}");
                return BadRequest("Account number is required");
                
            }

            try
            {
                var userDetails = await _userAccountService.GetUserDetailsByAccountNoAsync(accountNo);

                if (userDetails == null)
                {
                    _logger.LogError($"No details found for account number: {accountNo}");
                    return NotFound($"No details found for account number: {accountNo}");
                }

                return Ok(userDetails);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing request for account number: {AccountNo}", accountNo);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        [Authorize(Policy = "RequireRMRole")]
        [HttpGet("kachasiDetails/{FormNumber}")]
        public async Task<IActionResult> GetKachasiDetailsAsync(string FormNumber)
        {
            if (string.IsNullOrWhiteSpace(FormNumber))
            {
                _logger.LogError($"Form Number provided: {FormNumber}");
                return BadRequest("Form number is required");
            }

            try
            {
                var userDetails = await _userAccountService.GetKachasiDetailsAsync(FormNumber);

                if (userDetails == null)
                {
                    _logger.LogError($"No details found for form number: {FormNumber}");
                    return NotFound($"No details found for form number: {FormNumber}");
                }

                return Ok(userDetails);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing request for form number: {FormNo}", FormNumber);
                return StatusCode(500, "An error occurred while processing your request");
            }
        }

        //[HttpGet("kachasiDetails")]
        //public async Task<IActionResult> GetKachasiDetailAsync(string PickupListID, string FormNumber)
        //{
        //    if (string.IsNullOrWhiteSpace(FormNumber))
        //    {
        //        _logger.LogError($"Form Number provided: {FormNumber}");
        //        return BadRequest("Form number is required");
        //    }

        //    try
        //    {
        //        var userDetails = await _userAccountService.GetKachasiDetailAsync(PickupListID, FormNumber);

        //        if (userDetails == null)
        //        {
        //            _logger.LogError($"No details found for form number: {FormNumber}");
        //            return NotFound($"No details found for form number: {FormNumber}");
        //        }

        //        return Ok(userDetails);
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "Error processing request for form number: {FormNo}", FormNumber);
        //        return StatusCode(500, "An error occurred while processing your request");
        //    }
        //}

        //[HttpPost("submit-bid")]
        //public async Task<IActionResult> SubmitBidWithFiles([FromForm] string applicantJson, [FromForm] List<IFormFile> files)
        //{
        //    if (string.IsNullOrEmpty(applicantJson))
        //        return BadRequest("Applicant JSON is required.");

        //    // Deserialize the JSON into the ApplicantDetails model
        //    var applicant = JsonConvert.DeserializeObject<ApplicantDetails>(applicantJson);

        //    if (applicant == null)
        //        return BadRequest("Invalid applicant data.");

        //    applicant.Status = "Pending";
        //    applicant.CreatedAt =  DateTime.Now;
        //    applicant.ValueDate = DateTime.Now;
        //    // Save applicant to DB first so we get the Id
        //    _context.ApplicantDetails.Add(applicant);
        //    await _context.SaveChangesAsync();

        //    // Handle file uploads
        //    if (files != null && files.Any())
        //    {
        //        foreach (var file in files)
        //        {
        //            using var memoryStream = new MemoryStream();
        //            await file.CopyToAsync(memoryStream);
        //            var document = new Document
        //            {
        //                FileName = file.FileName,
        //                ContentType = file.ContentType,
        //                FileData = memoryStream.ToArray(),
        //                UploadedAt = DateTime.UtcNow,
        //                ApplicantDetailsId = applicant.Id // FK relationship
        //            };
        //            _context.Documents.Add(document);
        //        }

        //        await _context.SaveChangesAsync();
        //    }

        //    return Ok(new { Message = "Application submitted successfully." });
        //}



        //[HttpPost("{applicantId}/upload-files")]
        //public async Task<IActionResult> UploadFiles(int applicantId, List<IFormFile> files)
        //{
        //    var applicant = await _context.ApplicantDetails.FindAsync(applicantId);
        //    if (applicant == null)
        //        return NotFound("Applicant not found.");

        //    string uploadsFolder = Path.Combine(_env.WebRootPath, "uploads", applicantId.ToString());
        //    Directory.CreateDirectory(uploadsFolder); // create if not exists

        //    var savedDocs = new List<Document>();

        //    foreach (var file in files)
        //    {
        //        var fileName = Path.GetFileNameWithoutExtension(file.FileName);
        //        var extension = Path.GetExtension(file.FileName);
        //        var uniqueName = $"{Guid.NewGuid()}{extension}";
        //        var fullPath = Path.Combine(uploadsFolder, uniqueName);

        //        using (var fileStream = new FileStream(fullPath, FileMode.Create))
        //        {
        //            await file.CopyToAsync(fileStream);
        //        }

        //        var doc = new Document
        //        {
        //            FileName = file.FileName,
        //            FileType = file.ContentType,
        //            FileSize = file.Length,
        //            FilePath = Path.Combine("uploads", applicantId.ToString(), uniqueName),
        //            ApplicantDetailsId = applicantId
        //        };

        //        savedDocs.Add(doc);
        //    }

        //    _context.Documents.AddRange(savedDocs);
        //    await _context.SaveChangesAsync();

        //    return Ok(new { message = "Files uploaded", count = savedDocs.Count });
        //}
        [Authorize(Policy = "RequireRMRole")]
        [HttpPost("submit-bid")]
        public async Task<IActionResult> SubmitBidWithFiles([FromForm] string applicantJson, string RMEmail, [FromForm] List<IFormFile> files)
        {
            if (string.IsNullOrWhiteSpace(applicantJson))
                return BadRequest("applicantJson is required.");

            ApplicantDetails applicant;
            try
            {
                applicant = JsonConvert.DeserializeObject<ApplicantDetails>(applicantJson);
            }
            catch (Exception ex)
            {
                AppLogger.LogError($"Invalid JSON format: {ex.Message}");
                return BadRequest($"Invalid JSON format: {ex.Message}");
            }

            if (applicant == null)
                return BadRequest("Could not parse applicantJson into an ApplicantDetails object.");

            // Validate required fields
            var validationErrors = new List<string>();
            if (string.IsNullOrWhiteSpace(applicant.ApplicantName)) validationErrors.Add("ApplicantName is required.");
            if (string.IsNullOrWhiteSpace(applicant.AccountNumber)) validationErrors.Add("AccountNumber is required.");
            if (string.IsNullOrWhiteSpace(applicant.BVN)) validationErrors.Add("BVN is required.");
            if (string.IsNullOrWhiteSpace(applicant.FormMNumber)) validationErrors.Add("FormMNumber is required.");
            if (applicant.TotalCFValue <= 0) validationErrors.Add("TotalCFValue must be greater than zero.");
            if (applicant.PurchaseAmountUSD <= 0) validationErrors.Add("PurchaseAmountUSD must be greater than zero.");
            if (applicant.Rate <= 0) validationErrors.Add("Rate must be greater than zero.");

            if (validationErrors.Any())
            {
                AppLogger.LogError($"Validation failed: {validationErrors}");
                return BadRequest(new { Message = "Validation failed", Errors = validationErrors });
            }                
               

            // Set system fields
            applicant.Status = "Pending";
            applicant.CreatedAt = DateTime.UtcNow;
            applicant.ValueDate = DateTime.UtcNow;
            applicant.RMEmail = RMEmail;

            try
            {
                // Save applicant to DB first
                _context.ApplicantDetails.Add(applicant);
                await _context.SaveChangesAsync();
                AppLogger.LogInformation($"Files to add: {files.Count}");

                // Save files (if any)
                if (files != null && files.Any())
                {
                    foreach (var file in files)
                    {
                        using var memoryStream = new MemoryStream();
                        await file.CopyToAsync(memoryStream);

                        var document = new Document
                        {
                            FileName = file.FileName,
                            ContentType = file.ContentType,
                            FileData = memoryStream.ToArray(),
                            UploadedAt = DateTime.UtcNow,
                            ApplicantDetailsId = applicant.Id
                        };

                        _context.Documents.Add(document);

                    }

                    await _context.SaveChangesAsync();
                    AppLogger.LogInformation("Files Added");
                }

                var auditLog = new UserActivity
                {
                    ActivityId = Guid.NewGuid().ToString(),
                    UserName = RMEmail,
                    ActionType = "POST",
                    RequestUrl = "api/submit-bid",
                    Timestamp = DateTime.UtcNow.AddHours(1),
                    IpAddress = IpAddress(),
                    StatusCode = 200,
                };

                _context.UserActivities.Add(auditLog);
                await _context.SaveChangesAsync();

                var tradeEmails = await _context.Users
                                            .Where(u => u.RoleType == "TRADE" && u.IsActive)
                                            .Select(u => u.Email)
                                            .ToListAsync();

                var emailListString = string.Join(",", tradeEmails); // comma-separated string
                var mailModel = new BIDConfirmation.Services.SendMailModel
                {
                    From = "<EMAIL>",
                    To = emailListString,  // now dynamic list of trade team emails
                    Subject = "FORM M REQUEST ON BID CONFIRMATION PORTAL",
                    Body = @"Dear Team,<br><br>
                                A Form M has been submitted for your approval.<br><br>
                                Please log into BID Confirmation Portal immediately to treat this.<br><br>
                                Best regards."
                };

                await _email.SendEmail(mailModel);


                AppLogger.LogInformation($"Application submitted successfully:{applicant.Id}");
                return Ok(new { Message = "Application submitted successfully.", ApplicantId = applicant.Id });
            }
            catch (Exception ex)
            {
                // You can log this instead
                AppLogger.LogError($"An error occurred while saving the application: {ex.Message}");
                return StatusCode(500, $"An error occurred while saving the application: {ex.Message}");
            }
        }

        //[HttpPost("trade-review/{applicationId}")]
        //public async Task<IActionResult> TradeReview(Guid applicationId, [FromBody] ApprovalRequest request)
        //{
        //    try
        //    {
        //        var app = await _context.ApplicantDetails.FindAsync(applicationId);
        //        if (app == null)
        //        {
        //            AppLogger.LogError($"Application not found: {applicationId}");
        //            return Ok(new { success = false, message = "Application not found." });
        //        }

        //        if (app.Status != "Pending")
        //        {
        //            AppLogger.LogError($"Application not in Pending state: {applicationId}");
        //            return Ok(new { success = false, message = "Application not in Pending state." });
        //        }

        //        var rmEmail = app.RMEmail;
        //        if (request.Approved)
        //        {
        //            app.Status = "TradeAccepted";
        //            app.TradeReviewedAt = DateTime.Now;
        //            app.TradeReviewer = request.Reviewer;
        //            app.TransactionReference = request.TransactionReference;

        //            var treasuryEmails = await _context.Users
        //                                    .Where(u => u.RoleType == "TREASURY" && u.IsActive)
        //                                    .Select(u => u.Email)
        //                                    .ToListAsync();

        //            var emailListString = string.Join(",", treasuryEmails);
        //            var mailModel = new BIDConfirmation.Services.SendMailModel
        //            {
        //                From = "<EMAIL>",
        //                To = emailListString,
        //                Subject = "FORM M APPROVED BY TRADE TEAM",
        //                Body = $"Dear Team," +
        //                       $"<br><br>A Form M has been approved by the Trade Team." +
        //                       $"<br><br>Please log into BID Confirmation Portal immediately to treat this." +
        //                       $"<br><br>Best regards.",
        //            };
        //            await _email.SendEmail(mailModel);
        //        }
        //        else
        //        {
        //            app.Status = "TradeRejected";
        //            app.RejectionReason = request.Reason;
        //            app.TradeReviewedAt = DateTime.Now;
        //            app.TradeReviewer = request.Reviewer;
        //            app.TransactionReference = request.TransactionReference;

        //            //var emailTo = _config.GetSection("ExternalUrl").GetSection("RM").Value;
        //            var mailModel = new BIDConfirmation.Services.SendMailModel
        //            {
        //                From = "<EMAIL>",
        //                To = rmEmail,
        //                Subject = "FORM M REJECTED BY TRADE TEAM",
        //                Body = $"Dear RM," +
        //                       $"<br><br>A Form M has been rejected by the Trade Team." +
        //                       $"<br><br>Please log into BID Confirmation Portal immediately to treat this." +
        //                       $"<br><br>Best regards.",
        //            };
        //            await _email.SendEmail(mailModel);
        //        }

        //        await _context.SaveChangesAsync();


        //        return Ok(new
        //        {
        //            success = true,
        //            message = $"Trade review {(request.Approved ? "accepted" : "rejected")}."
        //        });
        //    }
        //    catch (Exception ex)
        //    {
        //        AppLogger.LogError($"Error occurred during trade review : {ex}");
        //        return StatusCode(500, new
        //        {
        //            success = false,
        //            message = "An unexpected error occurred. Please try again later."
        //        });
        //    }
        //}
        [Authorize(Policy = "RequireTradeRole")]
        [HttpPost("trade-review/{applicationId}")]
        public async Task<IActionResult> TradeReview(Guid applicationId, [FromBody] ApprovalRequest request)
        {
            try
            {
                var app = await _context.ApplicantDetails.FindAsync(applicationId);
                if (app == null)
                    return Ok(new { success = false, message = "Application not found." });

                // Enforce same-day condition
                if (app.CreatedAt.Date != DateTime.Now.Date)
                    return Ok(new { success = false, message = "This application is not available for Trade review (expired)." });

                if (app.Status != "Pending")
                    return Ok(new { success = false, message = "Application not in Pending state." });

                var rmEmail = app.RMEmail;

                if (request.Approved)
                {
                    app.Status = "TradeAccepted";
                    app.TradeReviewedAt = DateTime.Now;
                    app.TradeReviewer = request.Reviewer;
                    app.TransactionReference = request.TransactionReference;

                    // Notify Treasury
                    var treasuryEmails = await _context.Users
                                             .Where(u => u.RoleType == "TREASURY" && u.IsActive)
                                             .Select(u => u.Email)
                                             .ToListAsync();

                    await _email.SendEmail(new BIDConfirmation.Services.SendMailModel
                    {
                        From = "<EMAIL>",
                        To = string.Join(",", treasuryEmails),
                        Subject = "FORM M APPROVED BY TRADE TEAM",
                        Body = $"Dear Treasury Team," +
                               $"<br><br>A Form M has been approved by the Trade Team." +
                               $"<br><br>Please log into BID Confirmation Portal immediately to treat this." +
                               $"<br><br>Best regards."
                    });

                    // Notify RM
                    await _email.SendEmail(new BIDConfirmation.Services.SendMailModel
                    {
                        From = "<EMAIL>",
                        To = rmEmail,
                        Subject = "FORM M APPROVED BY TRADE TEAM",
                        Body = $"Dear RM," +
                               $"<br><br>Your Form M has been approved by the Trade Team." +
                               $"<br><br>Please log into BID Confirmation Portal for updates." +
                               $"<br><br>Best regards."
                    });
                }
                else
                {
                    app.Status = "TradeRejected";
                    app.RejectionReason = request.Reason;
                    app.TradeReviewedAt = DateTime.Now;
                    app.TradeReviewer = request.Reviewer;
                    app.TransactionReference = request.TransactionReference;

                    await _email.SendEmail(new BIDConfirmation.Services.SendMailModel
                    {
                        From = "<EMAIL>",
                        To = rmEmail,
                        Subject = "FORM M REJECTED BY TRADE TEAM",
                        Body = $"Dear RM," +
                               $"<br><br>A Form M has been rejected by the Trade Team." +
                               $"<br><br>Please log into BID Confirmation Portal immediately to treat this." +
                               $"<br><br>Best regards."
                    });
                }

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    success = true,
                    message = $"Trade review {(request.Approved ? "accepted" : "rejected")}."
                });
            }
            catch (Exception ex)
            {
                AppLogger.LogError($"Error occurred during trade review : {ex}");
                return StatusCode(500, new
                {
                    success = false,
                    message = "An unexpected error occurred. Please try again later."
                });
            }
        }


        [Authorize(Policy = "RequireTreasuryRole")]
        [HttpPost("treasury-review/{applicationId}")]
        public async Task<IActionResult> TreasuryReview(Guid applicationId, [FromBody] TreasuryApprovalRequest request)
        {
            try
            {
                var app = await _context.ApplicantDetails.FindAsync(applicationId);
                if (app == null)
                    return NotFound("Application not found.");

                // Enforce same-day condition
                if (app.CreatedAt.Date != DateTime.Now.Date)
                    return BadRequest("This application is not available for Treasury review (expired).");

                // Allow Treasury to act on TradeAccepted or PartiallyTreasuryAccepted
                if (app.Status != "TradeAccepted" && app.Status != "TreasuryPartiallyAccepted")
                    return BadRequest("Application not eligible for Treasury review.");

                //if (app.PurchaseAmountUSD == null)
                //    app.RequestedAmount = request.Amount ?? 0;

                if (app.TreatedAmount == null)
                    app.TreatedAmount = 0;

                if (app.Balance == null)
                    app.Balance = 0;

                // --- Treasury Review Logic ---
                app.TreasuryReviewedAt = DateTime.Now;
                app.TreasuryReviewer = request.Reviewer;

                if (!request.Approved)
                {
                    app.Status = "TreasuryRejected";
                    app.RejectionReason = request.Reason;
                }
                else
                {
                    if (app.PurchaseAmountUSD <= 0)
                        return BadRequest("RequestedAmount not set for this application.");

                    if (!request.Amount.HasValue || request.Amount.Value <= 0)
                        return BadRequest("Treasury must provide a valid treated amount.");

                    var approveAmount = request.Amount.Value;

                    // ✅ Ensure they don't overpay
                    if (approveAmount > app.Balance && app.Balance > 0)
                        return BadRequest("Approved amount cannot exceed outstanding balance.");

                    // Update amounts
                    app.TreatedAmount += approveAmount;
                    app.Balance = app.PurchaseAmountUSD - app.TreatedAmount;

                    // Status update
                    if (app.Balance > 0)
                        app.Status = "TreasuryPartiallyAccepted";
                    else
                        app.Status = "TreasuryAccepted";
                }


                // --- Email Notifications ---
                var tradeReviewerEmail = app.TradeReviewer;
                var rmEmail = app.RMEmail;

                var subject = request.Approved
                             ? (app.Status == "TreasuryPartiallyAccepted"
                                 ? "FORM M PARTIALLY APPROVED BY TREASURY"
                                 : "FORM M FULLY APPROVED BY TREASURY")
                             : "FORM M REJECTED BY TREASURY";

                var body = $"Dear Team,<br><br>" +
                           $"A Form M has been <strong>{(request.Approved ? (app.Status == "TreasuryPartiallyAccepted" ? "partially approved" : "fully approved") : "rejected")}</strong> by the Treasury Team.<br><br>" +
                           $"<strong>Reviewer:</strong> {request.Reviewer}<br>" +
                           $"<strong>FormM Number:</strong> {app.FormMNumber}<br>" +
                           $"<strong>Requested Amount:</strong> {app.PurchaseAmountUSD:N2}<br>" +
                           $"<strong>Treated Amount (so far):</strong> {app.TreatedAmount:N2}<br>" +
                           $"<strong>Balance:</strong> {app.Balance:N2}<br>" +
                           $"<strong>Rate:</strong> {(request.Rate.HasValue ? request.Rate.Value.ToString("N2") : "N/A")}<br>" +
                           $"<strong>Value Date:</strong> {(request.ValueDate.HasValue ? request.ValueDate.Value.ToString("yyyy-MM-dd") : "N/A")}<br>";

                if (!request.Approved)
                    body += $"<strong>Rejection Reason:</strong> {request.Reason}<br>";

                body += "<br>Please log into the BID Confirmation Portal for further processing.<br><br>Best regards.";

                var recipients = new List<string> { tradeReviewerEmail, rmEmail }
                    .Where(e => !string.IsNullOrWhiteSpace(e))
                    .Distinct()
                    .ToList();

                await _email.SendEmail(new BIDConfirmation.Services.SendMailModel
                {
                    From = "<EMAIL>",
                    To = string.Join(",", recipients),
                    Subject = subject,
                    Body = body
                });

                await _context.SaveChangesAsync();

                return Ok(new
                {
                    Success = true,
                    Message = $"Treasury review {(request.Approved ? app.Status : "rejected")}."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Treasury review failed.");
                return StatusCode(500, "An error occurred during treasury review.");
            }
        }





     
        [Authorize(Policy = "RequireTradeRole")]
        [HttpGet("trade/pending-requests")]
        public async Task<IActionResult> GetPendingRequestsForTrade()
        {
            var today = DateTime.UtcNow.Date; // Ensure you use .UtcNow or .Now depending on DB consistency

            var requests = await _context.ApplicantDetails
                .Where(a => a.Status == "Pending" && a.ValueDate.Date == today) // filter by today's date
                .Include(a => a.Documents)
                .ToListAsync();

            var result = requests.Select(a => new ApplicantDetailsDto
            {
                Id = a.Id,
                Status = a.Status,
                FormMNumber = a.FormMNumber,
                ApplicantAddress = a.ApplicantAddress,
                ApplicantName = a.ApplicantName,
                BeneficiaryName = a.BeneficiaryName,
                ApplicantPhone = a.ApplicantPhone,
                ApplicantTin = a.ApplicantTin,
                RCNumber = a.RCNumber,
                CountryOfSupply = a.CountryOfSupply,
                HSCode = a.HSCode,
                Currency = a.Currency,
                //Tolerance = a.Tolerance,
                TotalCFValue = a.TotalCFValue,
                FormMRegistrationDate = a.FormMRegistrationDate,
                GeneralGoodsDescription = a.GeneralGoodsDescription,
                PaymentMode = a.PaymentMode,
                AccountName = a.AccountName,
                AccountNumber = a.AccountNumber,
                BVN = a.BVN,
                CreatedAt = a.CreatedAt,
                ValueDate = a.ValueDate,
                PurchaseAmountInFormMCurrency = a.PurchaseAmountInFormMCurrency,
                Rate = a.Rate,
                AvailableBalance = a.AvailableBalance,
                PurchaseAmountUSD = a.PurchaseAmountUSD,

                Documents = a.Documents?.Select(d => new DocumentDto
                {
                    Id = d.Id,
                    FileName = d.FileName,
                    ContentType = d.ContentType,
                    UploadedAt = d.UploadedAt
                }).ToList()
            });

            return Ok(result);
        }


        //[HttpGet("Getapplicant/{id}")]
        //public async Task<IActionResult> GetApplicantById(Guid id)
        //{
        //    var applicant = await _context.ApplicantDetails
        //         .Include(a => a.Documents)
        //        .FirstOrDefaultAsync(a => a.Id == id);

        //    if (applicant == null)
        //        return NotFound("Applicant not found.");
        //    var result = applicant.Select(a => new ApplicantDetailsDto
        //    {
        //        Id = a.Id,
        //        Status = a.Status,
        //        FormMNumber = a.FormMNumber,
        //        ApplicantAddress = a.ApplicantAddress,
        //        ApplicantName = a.ApplicantName,
        //        BeneficiaryName = a.BeneficiaryName,
        //        ApplicantPhone = a.ApplicantPhone,
        //        ApplicantTin = a.ApplicantTin,
        //        RCNumber = a.RCNumber,
        //        CountryOfSupply = a.CountryOfSupply,
        //        HSCode = a.HSCode,
        //        Currency = a.Currency,
        //        Tolerance = a.Tolerance,
        //        TotalCFValue = a.TotalCFValue,
        //        FormMRegistrationDate = a.FormMRegistrationDate,
        //        GeneralGoodsDescription = a.GeneralGoodsDescription,
        //        PaymentMode = a.PaymentMode,
        //        AccountName = a.AccountName,
        //        AccountNumber = a.AccountNumber,
        //        BVN = a.BVN,
        //        CreatedAt = a.CreatedAt,
        //        ValueDate = a.ValueDate,
        //        PurchaseAmountInFormMCurrency = a.PurchaseAmountInFormMCurrency,
        //        Rate = a.Rate,
        //        AvailableBalance = a.AvailableBalance,
        //        TradeReviewer = a.TradeReviewer,
        //        TradeReviewedAt = a.TradeReviewedAt,
        //        PurchaseAmountUSD = a.PurchaseAmountUSD,


        //        Documents = a.Documents?.Select(d => new DocumentDto
        //        {
        //            Id = d.Id,
        //            FileName = d.FileName,
        //            ContentType = d.ContentType,
        //            UploadedAt = d.UploadedAt
        //        }).ToList()
        //    });

        //    return Ok(result);

        //    return Ok(applicant);
        //}

        [HttpGet("Getapplicant/{id}")]
        public async Task<IActionResult> GetApplicantById(Guid id)
        {
            var applicant = await _context.ApplicantDetails
                .Include(a => a.Documents)
                .FirstOrDefaultAsync(a => a.Id == id);

            if (applicant == null)
                return NotFound("Applicant not found.");

            var result = new ApplicantDetailsDto
            {
                Id = applicant.Id,
                Status = applicant.Status,
                FormMNumber = applicant.FormMNumber,
                ApplicantAddress = applicant.ApplicantAddress,
                ApplicantName = applicant.ApplicantName,
                BeneficiaryName = applicant.BeneficiaryName,
                ApplicantPhone = applicant.ApplicantPhone,
                ApplicantTin = applicant.ApplicantTin,
                RCNumber = applicant.RCNumber,
                CountryOfSupply = applicant.CountryOfSupply,
                HSCode = applicant.HSCode,
                Currency = applicant.Currency,
                //Tolerance = applicant.Tolerance,
                TotalCFValue = applicant.TotalCFValue,
                FormMRegistrationDate = applicant.FormMRegistrationDate,
                GeneralGoodsDescription = applicant.GeneralGoodsDescription,
                PaymentMode = applicant.PaymentMode,
                AccountName = applicant.AccountName,
                AccountNumber = applicant.AccountNumber,
                BVN = applicant.BVN,
                CreatedAt = applicant.CreatedAt,
                ValueDate = applicant.ValueDate,
                PurchaseAmountInFormMCurrency = applicant.PurchaseAmountInFormMCurrency,
                Rate = applicant.Rate,
                AvailableBalance = applicant.AvailableBalance,
                TradeReviewer = applicant.TradeReviewer,
                TradeReviewedAt = applicant.TradeReviewedAt,
                PurchaseAmountUSD = applicant.PurchaseAmountUSD,
                RejectionReason = applicant.RejectionReason,
                Documents = applicant.Documents?.Select(d => new DocumentDto
                {
                    Id = d.Id,
                    FileName = d.FileName,
                    ContentType = d.ContentType,
                    UploadedAt = d.UploadedAt
                }).ToList()
                
            };

            return Ok(result);
        }


        [HttpGet("GetAllApplicants")]
        public async Task<IActionResult> GetAllApplicants()
        {
            var applicant = await _context.ApplicantDetails.Include(a => a.Documents).ToListAsync();

            var result = applicant.Select(a => new ApplicantDetailsDto
            {
                Id = a.Id,
                Status = a.Status,
                FormMNumber = a.FormMNumber,
                ApplicantAddress = a.ApplicantAddress,
                ApplicantName = a.ApplicantName,
                BeneficiaryName = a.BeneficiaryName,
                ApplicantPhone = a.ApplicantPhone,
                ApplicantTin = a.ApplicantTin,
                RCNumber = a.RCNumber,
                CountryOfSupply = a.CountryOfSupply,
                HSCode = a.HSCode,
                Currency = a.Currency,
                //Tolerance = a.Tolerance,
                TotalCFValue = a.TotalCFValue,
                FormMRegistrationDate = a.FormMRegistrationDate,
                GeneralGoodsDescription = a.GeneralGoodsDescription,
                PaymentMode = a.PaymentMode,
                AccountName = a.AccountName,
                AccountNumber = a.AccountNumber,
                BVN = a.BVN,
                CreatedAt = a.CreatedAt,
                ValueDate = a.ValueDate,
                PurchaseAmountInFormMCurrency = a.PurchaseAmountInFormMCurrency,
                Rate = a.Rate,
                AvailableBalance = a.AvailableBalance,
                TradeReviewer = a.TradeReviewer,
                TradeReviewedAt = a.TradeReviewedAt,
                PurchaseAmountUSD = a.PurchaseAmountUSD,
                RejectionReason = a.RejectionReason,


                Documents = a.Documents?.Select(d => new DocumentDto
                {
                    Id = d.Id,
                    FileName = d.FileName,
                    ContentType = d.ContentType,
                    UploadedAt = d.UploadedAt
                }).ToList()
            });

            return Ok(result);
        }



        [HttpGet("documents/{id}/download")]
        public async Task<IActionResult> DownloadTradeDocument(Guid id)
        {
            var document = await _context.Documents.FindAsync(id);

            if (document == null)
                return NotFound("Document not found.");

            return File(
                document.FileData,
                document.ContentType ?? "application/octet-stream",
                document.FileName
            );
        }

        //[HttpGet("documents/{documentId}/Treasurydownload")]
        //public async Task<IActionResult> DownloadDocument(Guid documentId)
        //{
        //    var document = await _context.Documents.FindAsync(documentId);
        //    if (document == null)
        //        return NotFound();

        //    var userIdClaim = User.FindFirst("UserId")?.Value;
        //    if (!Guid.TryParse(userIdClaim, out Guid userId))
        //        return Unauthorized("Invalid UserId in token");

        //    var alreadyDownloaded = await _context.DocumentDownloads
        //        .AnyAsync(d => d.DocumentId == documentId && d.UserId == userId);

        //    if (!alreadyDownloaded)
        //    {
        //        _context.DocumentDownloads.Add(new DocumentDownload
        //        {
        //            DocumentId = documentId,
        //            UserId = userId
        //        });
        //        await _context.SaveChangesAsync();
        //    }

        //    return File(
        //        document.FileData,
        //        document.ContentType ?? "application/octet-stream",
        //        document.FileName
        //    );
        //}

        [HttpGet("documents/{documentId}/has-downloaded")]
        public async Task<IActionResult> HasDownloaded(Guid documentId)
        {
            var userIdClaim = User.FindFirst("UserId")?.Value;
            if (!Guid.TryParse(userIdClaim, out Guid userId))
                return Unauthorized("Invalid UserId in token");

            var hasDownloaded = await _context.DocumentDownloads
                .AnyAsync(d => d.DocumentId == documentId && d.UserId == userId);

            return Ok(new { success = true, hasDownloaded });
        }

        [HttpGet("documents/GetAlldownloaded")]
        public async Task<IActionResult> GetDownloadedDocuments()
        {
            var userIdClaim = User.FindFirst("UserId")?.Value;
            if (!Guid.TryParse(userIdClaim, out Guid userId))
                return Unauthorized("Invalid UserId in token");

            var downloadedDocs = await _context.DocumentDownloads
                .Where(d => d.UserId == userId)
                .Include(d => d.Document) // 👈 Eager load the actual document info
                .Select(d => new
                {
                    d.Document.Id,
                    d.Document.FileName,
                    d.Document.ContentType,
                    DownloadedAt = d.DownloadedAt // Add this property to DocumentDownload if needed
                })
                .ToListAsync();

            return Ok(new { success = true, documents = downloadedDocs });
        }

        [Authorize(Policy = "RequireTreasuryRole")]
        [HttpGet("Treasury-requests")]
        public async Task<IActionResult> GetTradeApprovedRequests()
        {
            var today = DateTime.UtcNow.Date;
            var validStatuses = new[] { "TradeAccepted", "TreasuryAccepted", "TreasuryRejected", "TreasuryPartiallyAccepted" };

            var result = await _context.ApplicantDetails
                .Where(a => validStatuses.Contains(a.Status) && a.ValueDate.Date == today) // only today's data
                .Include(a => a.Documents)
                .Select(a => new ApplicantDetailsDto
                {
                    Id = a.Id,
                    Status = a.Status,
                    FormMNumber = a.FormMNumber,
                    ApplicantAddress = a.ApplicantAddress,
                    ApplicantName = a.ApplicantName,
                    BeneficiaryName = a.BeneficiaryName,
                    ApplicantPhone = a.ApplicantPhone,
                    ApplicantTin = a.ApplicantTin,
                    RCNumber = a.RCNumber,
                    CountryOfSupply = a.CountryOfSupply,
                    HSCode = a.HSCode,
                    Currency = a.Currency,
                    //Tolerance = a.Tolerance,
                    TotalCFValue = a.TotalCFValue,
                    FormMRegistrationDate = a.FormMRegistrationDate,
                    GeneralGoodsDescription = a.GeneralGoodsDescription,
                    PaymentMode = a.PaymentMode,
                    AccountName = a.AccountName,
                    AccountNumber = a.AccountNumber,
                    BVN = a.BVN,
                    CreatedAt = a.CreatedAt,
                    ValueDate = a.ValueDate,
                    PurchaseAmountInFormMCurrency = a.PurchaseAmountInFormMCurrency,
                    Rate = a.Rate,
                    AvailableBalance = a.AvailableBalance,
                    TradeReviewer = a.TradeReviewer,
                    TradeReviewedAt = a.TradeReviewedAt,
                    PurchaseAmountUSD = a.PurchaseAmountUSD,
                    IsDownloaded = a.IsDownloaded,
                    TransactionReference = a.TransactionReference,
                    TreatedAmount = a.TreatedAmount,
                    Balance = a.Balance,

                    Documents = a.Documents.Select(d => new DocumentDto
                    {
                        Id = d.Id,
                        FileName = d.FileName,
                        ContentType = d.ContentType,
                        UploadedAt = d.UploadedAt
                    }).ToList()
                })
                .ToListAsync();

            return Ok(result);
        }


        [Authorize(Policy = "RequireRMRole")]
        [HttpGet("rm/rejected-requests")]
        public async Task<IActionResult> GetRejectedRequestsForRM()
        {
            var rejectedRequests = await _context.ApplicantDetails
                .Where(a => a.Status == "TradeRejected" || a.Status == "TreasuryRejected")
                .OrderByDescending(a => a.CreatedAt)
                .ToListAsync();

            return Ok(rejectedRequests);
        }

        //[HttpGet("Treasury-requests")]
        //public async Task<IActionResult> TresuryRequests()
        //{
        //    var TreasuryRequests = await _context.ApplicantDetails
        //        .Where(a => a.Status == "TreasuryAccepted" || a.Status == "TreasuryRejected")
        //        .OrderByDescending(a => a.CreatedAt)
        //        .ToListAsync();

        //    return Ok(TreasuryRequests);
        //}
        [Authorize(Policy = "RequireTreasuryRole")]
        [HttpGet("export-applicant/{id}")]
        public async Task<IActionResult> ExportSingleApplicantToExcel(Guid id)
        {
            var applicant = await _context.ApplicantDetails
                .FirstOrDefaultAsync(a => a.Id == id);

            if (applicant == null)
                return NotFound("Applicant not found.");

            using (var workbook = new XLWorkbook())
            {
                var worksheet = workbook.Worksheets.Add("Applicant Details");

                // Headers
               // worksheet.Cell(1, 1).Value = "Id";
                worksheet.Cell(1, 1).Value = "FormMNumber";
                worksheet.Cell(1, 2).Value = "Status";
                worksheet.Cell(1, 3).Value = "ApplicantAddress";
                worksheet.Cell(1, 4).Value = "ApplicantName";
                worksheet.Cell(1, 5).Value = "BeneficiaryName";
                worksheet.Cell(1, 6).Value = "ApplicantPhone";
                worksheet.Cell(1, 7).Value = "ApplicantTin";
                worksheet.Cell(1, 8).Value = "RCNumber";
                worksheet.Cell(1, 9).Value = "CountryOfSupply";
                worksheet.Cell(1, 10).Value = "HSCode";
                worksheet.Cell(1, 11).Value = "Currency";
                worksheet.Cell(1, 12).Value = "Tolerance";
                worksheet.Cell(1, 13).Value = "TotalCFValue";
                worksheet.Cell(1, 14).Value = "FormMRegistrationDate";
                worksheet.Cell(1, 15).Value = "GeneralGoodsDescription";
                worksheet.Cell(1, 16).Value = "PaymentMode";
                worksheet.Cell(1, 17).Value = "AccountName";
                worksheet.Cell(1, 18).Value = "AccountNumber";
                worksheet.Cell(1, 19).Value = "BVN";
                worksheet.Cell(1, 20).Value = "CreatedAt";
                worksheet.Cell(1, 21).Value = "ValueDate";
                worksheet.Cell(1, 22).Value = "PurchaseAmountInFormMCurrency";
                worksheet.Cell(1, 23).Value = "Rate";
                worksheet.Cell(1, 24).Value = "AvailableBalance";
                worksheet.Cell(1, 25).Value = "TradeReviewer";
                worksheet.Cell(1, 26).Value = "TradeReviewedAt";
                worksheet.Cell(1, 27).Value = "PurchaseAmountUSD";
                worksheet.Cell(1, 28).Value = "TransactionReference";
                worksheet.Cell(1, 29).Value = "TreatedAmount";
                worksheet.Cell(1, 30).Value = "Balance";
                worksheet.Cell(1, 31).Value = "RejectionReason";
                //worksheet.Cell(1, 29).Value = "IsExported";

                // Values
                // worksheet.Cell(2, 1).Value = applicant.Id;
                worksheet.Cell(2, 1).Value = applicant.FormMNumber;
                worksheet.Cell(2, 2).Value = applicant.Status;
                worksheet.Cell(2, 3).Value = applicant.ApplicantAddress;
                worksheet.Cell(2, 4).Value = applicant.ApplicantName;
                worksheet.Cell(2, 5).Value = applicant.BeneficiaryName;
                worksheet.Cell(2, 6).Value = applicant.ApplicantPhone;
                worksheet.Cell(2, 7).Value = applicant.ApplicantTin;
                worksheet.Cell(2, 8).Value = applicant.RCNumber;
                worksheet.Cell(2, 9).Value = applicant.CountryOfSupply;
                worksheet.Cell(2, 10).Value = applicant.HSCode;
                worksheet.Cell(2, 11).Value = applicant.Currency;
                worksheet.Cell(2, 12).Value = applicant.Tolerance;
                worksheet.Cell(2, 13).Value = applicant.TotalCFValue;
                worksheet.Cell(2, 14).Value = applicant.FormMRegistrationDate;
                worksheet.Cell(2, 15).Value = applicant.GeneralGoodsDescription;
                worksheet.Cell(2, 16).Value = applicant.PaymentMode;
                worksheet.Cell(2, 17).Value = applicant.AccountName;
                worksheet.Cell(2, 18).Value = applicant.AccountNumber;
                worksheet.Cell(2, 19).Value = applicant.BVN;
                worksheet.Cell(2, 20).Value = applicant.CreatedAt;
                worksheet.Cell(2, 21).Value = applicant.ValueDate;
                worksheet.Cell(2, 22).Value = applicant.PurchaseAmountInFormMCurrency;
                worksheet.Cell(2, 23).Value = applicant.Rate;
                worksheet.Cell(2, 24).Value = applicant.AvailableBalance;
                worksheet.Cell(2, 25).Value = applicant.TradeReviewer;
                worksheet.Cell(2, 26).Value = applicant.TradeReviewedAt;
                worksheet.Cell(2, 27).Value = applicant.PurchaseAmountUSD;
                worksheet.Cell(2, 28).Value = applicant.TransactionReference;
                worksheet.Cell(2, 29).Value = applicant.TreatedAmount;
                worksheet.Cell(2, 30).Value = applicant.Balance;
                worksheet.Cell(2, 31).Value = applicant.RejectionReason;
                //worksheet.Cell(2, 30).Value = applicant.IsExported;

                // Auto-adjust column widths
                worksheet.Columns().AdjustToContents();

                // Save Excel to memory stream
                using var stream = new MemoryStream();
                workbook.SaveAs(stream);
                stream.Position = 0;

                applicant.IsDownloaded = true;
                _context.ApplicantDetails.Update(applicant);
                await _context.SaveChangesAsync();

                var fileName = $"Applicant_{applicant.ApplicantName}.xlsx";
                return File(stream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
        }


        //[Authorize]
        //[HttpGet("export/all-applicants")]
        //public async Task<IActionResult> ExportAllApplicantsToExcel()
        //{
        //    var applicants = await _context.ApplicantDetails
        //        .Where(a => !a.IsDownloaded && a.Status == "Pending")
        //        .ToListAsync();

        //    if (!applicants.Any())
        //        return BadRequest(new { success = false, message = "No new applicants to export." });

        //    using var workbook = new XLWorkbook();
        //    var worksheet = workbook.Worksheets.Add("Applicants");

        //    // Header
        //    worksheet.Cell(1, 1).Value = "Applicant Name";
        //    worksheet.Cell(1, 2).Value = "Status";
        //    worksheet.Cell(1, 3).Value = "Form M Number";
        //    worksheet.Cell(1, 4).Value = "Created At";

        //    int row = 2;
        //    foreach (var applicant in applicants)
        //    {
        //        worksheet.Cell(row, 1).Value = applicant.ApplicantName;
        //        worksheet.Cell(row, 2).Value = applicant.Status;
        //        worksheet.Cell(row, 3).Value = applicant.FormMNumber;
        //        worksheet.Cell(row, 4).Value = applicant.CreatedAt.ToString("yyyy-MM-dd HH:mm");
        //        row++;

        //        // Mark each as downloaded
        //        applicant.IsDownloaded = true;
        //    }

        //    await _context.SaveChangesAsync();

        //    using var stream = new MemoryStream();
        //    workbook.SaveAs(stream);
        //    stream.Position = 0;

        //    return File(stream.ToArray(),
        //        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        //        $"all_applicants_{DateTime.UtcNow:yyyyMMddHHmmss}.xlsx");
        //}



        [Authorize(Roles = "Admin")]
        [HttpPost("reset-download/{applicantId}")]
        public async Task<IActionResult> ResetDownload(Guid applicantId)
        {
            var applicant = await _context.ApplicantDetails.FindAsync(applicantId);
            if (applicant == null)
                return NotFound("Applicant not found.");

            applicant.IsDownloaded = false;
            await _context.SaveChangesAsync();

            return Ok(new { success = true, message = "Download flag reset." });
        }

        private string IpAddress()
        {
            if (Request.Headers.ContainsKey("X-Forwarded-For"))
                return Request.Headers["X-Forwarded-For"];
            else
                return HttpContext.Connection.RemoteIpAddress.MapToIPv4().ToString();
        }




    }
}