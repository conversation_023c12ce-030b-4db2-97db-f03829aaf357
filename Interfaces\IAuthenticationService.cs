﻿using BidConfirmationAPI.Models;

namespace BidConfirmationAPI.Interfaces
{
    public interface IAuthenticationService
    {
        Task<Result> CreateUser(UserProfileCreate entity);
        Task<Result> LoginAsync(LoginCreate entity, string ipAddress);
        Task<Result> VerifyTokenAsync(TokenVerificationRequest request);
        //Task<Result> IWemaLogin(LoginCreate entity);
        string AuthenticateStaff(string StaffEmail, string StaffPassword);
        //Task<Result> ChangePassword(PasswordChange passwordChange);
        bool ValidateToken(string username, string token);
        Task<UserProfile> GetUserByEmailAsync(string email);
        Task<object> UpdateUserAsync(UserProfileUpdate profileUpdate);
    }
}
