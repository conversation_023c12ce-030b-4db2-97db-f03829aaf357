﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace BIDConfirmation.Migrations
{
    public partial class AddedTokenUsage : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsUsed",
                table: "Tokens",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "Nonce",
                table: "Tokens",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "UsedAt",
                table: "Tokens",
                type: "datetime2",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsUsed",
                table: "Tokens");

            migrationBuilder.DropColumn(
                name: "Nonce",
                table: "Tokens");

            migrationBuilder.DropColumn(
                name: "UsedA<PERSON>",
                table: "Tokens");
        }
    }
}
