# BID Confirmation Portal API

## Project Overview

The BID Confirmation Portal API is a comprehensive .NET 6 Web API system designed to manage and process foreign exchange (FX) bid confirmations for Form M applications in the banking sector. This system facilitates the workflow between Relationship Managers (RMs), Trade teams, and Treasury teams for processing foreign currency purchase requests.

## Business Context

### Purpose
The system automates the approval workflow for Form M applications, which are regulatory documents required for foreign currency purchases in Nigeria. It streamlines the process from initial submission by RMs through trade team review to final treasury approval.

### Key Business Processes
1. **Form M Submission**: RMs submit Form M applications with supporting documents
2. **Trade Review**: Trade team reviews and approves/rejects applications
3. **Treasury Processing**: Treasury team processes approved applications and sets exchange rates
4. **Document Management**: Secure upload, storage, and download of supporting documents
5. **Audit Trail**: Complete tracking of all actions and status changes

## System Architecture Overview

### Technology Stack
- **Framework**: .NET 6 Web API
- **Database**: SQL Server with Entity Framework Core 6.0
- **Authentication**: JWT Bearer tokens with LDAP integration
- **Authorization**: Role-based access control (RBAC)
- **Logging**: NLog for structured logging
- **Documentation**: Swagger/OpenAPI
- **Data Access**: Entity Framework Core + Dapper for complex queries
- **Email**: Custom email service integration
- **File Storage**: Database blob storage for documents

### Key Components
- **Controllers**: API endpoints for different user roles
- **Services**: Business logic implementation
- **Repositories**: Data access layer
- **Models**: Entity definitions and DTOs
- **Middleware**: Custom authentication and cutoff time enforcement
- **Database**: SQL Server with Entity Framework migrations

## User Roles and Permissions

### 1. Relationship Manager (RM)
- Submit Form M applications with documents
- View account details and Form M information
- Access restricted to business hours (before 3:00 PM)

### 2. Trade Team
- Review and approve/reject Form M applications
- Add transaction references
- Notify treasury team of approvals

### 3. Treasury Team
- Process trade-approved applications
- Set exchange rates and value dates
- Approve final transactions
- Download processed documents

### 4. Admin
- Create and manage user accounts
- System administration functions

## Key Features

### Security Features
- JWT-based authentication with refresh tokens
- LDAP integration for staff authentication
- Role-based authorization policies
- Rate limiting for failed login attempts
- Audit logging for all user activities
- Time-based access restrictions

### Business Features
- Multi-step approval workflow
- Document upload and management
- Email notifications at each workflow stage
- Real-time status tracking
- Exchange rate management
- Transaction reference tracking

### Technical Features
- RESTful API design
- Comprehensive error handling
- Structured logging with NLog
- Database migrations for schema management
- CORS support for frontend integration
- Swagger documentation

## Project Structure

```
BidConfirmationAPI/
├── Controllers/           # API controllers
├── Services/             # Business logic services
├── Repositories/         # Data access repositories
├── Models/              # Entity models
├── Data/                # Database context
├── Interfaces/          # Service contracts
├── Migrations/          # EF Core migrations
├── Properties/          # Launch settings
├── appsettings.json     # Configuration
├── Program.cs           # Application entry point
└── nlog.config         # Logging configuration
```

## Getting Started

This documentation provides comprehensive guidance for developers to understand, set up, and work with the BID Confirmation Portal API system. The following sections will cover detailed setup instructions, API documentation, and development guidelines.

## Documentation Sections

1. [System Architecture](docs/ARCHITECTURE.md) - Detailed technical architecture
2. [Setup Guide](docs/SETUP.md) - Installation and configuration
3. [API Documentation](docs/API.md) - Endpoint documentation
4. [Authentication & Security](docs/SECURITY.md) - Security implementation
5. [Business Workflows](docs/WORKFLOWS.md) - Process documentation
6. [Database Schema](docs/DATABASE.md) - Data model documentation
7. [Configuration](docs/CONFIGURATION.md) - Settings and deployment
8. [Developer Guide](docs/DEVELOPMENT.md) - Development guidelines
9. [Troubleshooting](docs/TROUBLESHOOTING.md) - Common issues and solutions

## Quick Start

1. Clone the repository
2. Configure database connection in `appsettings.json`
3. Run database migrations
4. Configure LDAP settings
5. Start the application
6. Access Swagger UI at `/swagger`

For detailed setup instructions, see the [Setup Guide](docs/SETUP.md).

## Support

For technical support or questions about this system, please refer to the troubleshooting guide or contact the development team.
