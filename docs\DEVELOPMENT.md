# Developer Guidelines

## Overview

This document provides comprehensive guidelines for developers working on the BID Confirmation Portal API. It covers coding standards, project structure, development workflows, testing approaches, and contribution guidelines.

## Development Environment Setup

### Required Tools

1. **Visual Studio 2022** (Community, Professional, or Enterprise)
2. **.NET 6 SDK** (6.0.36 or later)
3. **SQL Server Developer Edition** or **SQL Server Express**
4. **Git** for version control
5. **Postman** or **Insomnia** for API testing
6. **SQL Server Management Studio (SSMS)**

### IDE Configuration

**Visual Studio Extensions:**
- **Entity Framework Core Power Tools**
- **NLog Intellisense**
- **REST Client**
- **Code Cleanup on Save**

**VS Code Extensions (Alternative):**
- **C# for Visual Studio Code**
- **NuGet Package Manager**
- **REST Client**
- **GitLens**

## Project Structure

### Directory Organization

```
BidConfirmationAPI/
├── Controllers/              # API Controllers
│   ├── AdminController.cs
│   ├── UserAccountController.cs
│   └── WeatherForecastController.cs
├── Services/                 # Business Logic Services
│   ├── AuthenticationService.cs
│   ├── UserAccountService.cs
│   ├── EmailService.cs
│   └── SessionManager.cs
├── Repositories/             # Data Access Layer
│   └── UserAccountRepository.cs
├── Models/                   # Entity Models
│   ├── UserProfile.cs
│   ├── ApplicantDetails.cs
│   ├── Document.cs
│   └── ...
├── Data/                     # Database Context
│   └── ApplicationDbContext.cs
├── Interfaces/               # Service Contracts
│   ├── IAuthenticationService.cs
│   ├── IUserAccountService.cs
│   └── ...
├── Migrations/               # EF Core Migrations
├── Properties/               # Project Properties
├── docs/                     # Documentation
├── appsettings.json         # Configuration
├── nlog.config             # Logging Configuration
└── Program.cs              # Application Entry Point
```

### Naming Conventions

**Classes and Interfaces:**
- **PascalCase**: `UserAccountService`, `IAuthenticationService`
- **Interface Prefix**: All interfaces start with `I`
- **Descriptive Names**: Clear, meaningful names

**Methods and Properties:**
- **PascalCase**: `GetUserDetails()`, `IsActive`
- **Verb-Noun Pattern**: `CreateUser()`, `ValidateToken()`
- **Boolean Properties**: Start with `Is`, `Has`, `Can`

**Variables and Parameters:**
- **camelCase**: `userName`, `accountNumber`
- **Descriptive Names**: Avoid abbreviations

**Constants:**
- **PascalCase**: `MaxRetryAttempts`, `DefaultPageSize`

## Coding Standards

### C# Coding Guidelines

**File Organization:**
```csharp
// 1. Using statements (grouped and sorted)
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using BidConfirmationAPI.Models;

// 2. Namespace declaration
namespace BidConfirmationAPI.Controllers
{
    // 3. Class declaration with XML documentation
    /// <summary>
    /// Handles user account operations and bid management
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class UserAccountController : ControllerBase
    {
        // 4. Private fields
        private readonly IUserAccountService _userAccountService;
        private readonly ILogger<UserAccountController> _logger;

        // 5. Constructor
        public UserAccountController(
            IUserAccountService userAccountService,
            ILogger<UserAccountController> logger)
        {
            _userAccountService = userAccountService;
            _logger = logger;
        }

        // 6. Public methods
        // 7. Private methods
    }
}
```

**Method Structure:**
```csharp
/// <summary>
/// Retrieves user details by account number
/// </summary>
/// <param name="accountNo">The account number to search for</param>
/// <returns>User account details or NotFound if not exists</returns>
[HttpGet("details/{accountNo}")]
[Authorize(Policy = "RequireRMRole")]
public async Task<IActionResult> GetDetailsByAccountNo(string accountNo)
{
    // 1. Input validation
    if (string.IsNullOrWhiteSpace(accountNo))
    {
        _logger.LogError("Account number provided: {AccountNo}", accountNo);
        return BadRequest("Account number is required");
    }

    try
    {
        // 2. Business logic
        var userDetails = await _userAccountService.GetUserDetailsByAccountNoAsync(accountNo);

        // 3. Result handling
        if (userDetails == null)
        {
            _logger.LogError("No details found for account number: {AccountNo}", accountNo);
            return NotFound($"No details found for account number: {accountNo}");
        }

        return Ok(userDetails);
    }
    catch (Exception ex)
    {
        // 4. Error handling
        _logger.LogError(ex, "Error retrieving details for account: {AccountNo}", accountNo);
        return StatusCode(500, "An error occurred while processing your request");
    }
}
```

### Error Handling Standards

**Exception Handling Pattern:**
```csharp
public async Task<Result> ProcessApplicationAsync(ApplicantDetails application)
{
    try
    {
        // Validate input
        if (application == null)
            return new ErrorResult("Application data is required");

        // Business logic
        var result = await _repository.SaveAsync(application);
        
        // Log success
        _logger.LogInformation("Application processed successfully: {Id}", application.Id);
        
        return new SuccessResult { Content = result };
    }
    catch (ValidationException ex)
    {
        _logger.LogWarning(ex, "Validation failed for application: {Id}", application?.Id);
        return new ErrorResult(ex.Message);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Unexpected error processing application: {Id}", application?.Id);
        return new ErrorResult("An unexpected error occurred");
    }
}
```

**Custom Result Types:**
```csharp
public abstract class Result
{
    public bool Success { get; set; }
    public string Message { get; set; }
    public int Status { get; set; }
}

public class SuccessResult : Result
{
    public SuccessResult()
    {
        Success = true;
        Status = 200;
    }
    
    public object Content { get; set; }
}

public class ErrorResult : Result
{
    public ErrorResult(string message)
    {
        Success = false;
        Message = message;
        Status = 400;
    }
}
```

### Logging Standards

**Structured Logging:**
```csharp
// Good - Structured logging with parameters
_logger.LogInformation("User {UserId} submitted application {ApplicationId} at {Timestamp}", 
    userId, applicationId, DateTime.UtcNow);

// Bad - String concatenation
_logger.LogInformation("User " + userId + " submitted application " + applicationId);
```

**Log Levels:**
- **Trace**: Very detailed information
- **Debug**: Debugging information
- **Information**: General information
- **Warning**: Potentially harmful situations
- **Error**: Error events that allow application to continue
- **Critical**: Very serious error events

## Database Development

### Entity Framework Guidelines

**Entity Configuration:**
```csharp
public class ApplicantDetails
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();
    
    [Required]
    [MaxLength(100)]
    public string FormMNumber { get; set; }
    
    [Required]
    [MaxLength(200)]
    public string ApplicantName { get; set; }
    
    // Navigation properties
    public ICollection<Document> Documents { get; set; }
}
```

**DbContext Configuration:**
```csharp
protected override void OnModelCreating(ModelBuilder builder)
{
    base.OnModelCreating(builder);

    // Configure relationships
    builder.Entity<ApplicantDetails>()
           .HasMany(a => a.Documents)
           .WithOne(d => d.Applicant)
           .HasForeignKey(d => d.ApplicantDetailsId)
           .OnDelete(DeleteBehavior.Cascade);

    // Configure indexes
    builder.Entity<ApplicantDetails>()
           .HasIndex(a => a.FormMNumber)
           .IsUnique();

    // Configure default values
    builder.Entity<Document>()
           .Property(d => d.UploadedAt)
           .HasDefaultValueSql("GETUTCDATE()");
}
```

**Migration Best Practices:**
```bash
# Create migration with descriptive name
dotnet ef migrations add AddUserProfileTable

# Review migration before applying
# Check generated SQL in migration file

# Apply migration
dotnet ef database update

# Rollback if needed
dotnet ef database update PreviousMigrationName
```

## API Development Guidelines

### Controller Design

**RESTful Conventions:**
```csharp
[ApiController]
[Route("api/[controller]")]
public class UserAccountController : ControllerBase
{
    // GET api/useraccount
    [HttpGet]
    public async Task<IActionResult> GetAll([FromQuery] UserQueryParameters parameters)
    
    // GET api/useraccount/5
    [HttpGet("{id}")]
    public async Task<IActionResult> GetById(Guid id)
    
    // POST api/useraccount
    [HttpPost]
    public async Task<IActionResult> Create([FromBody] UserCreateRequest request)
    
    // PUT api/useraccount/5
    [HttpPut("{id}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UserUpdateRequest request)
    
    // DELETE api/useraccount/5
    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(Guid id)
}
```

**Authorization Patterns:**
```csharp
// Role-based authorization
[Authorize(Policy = "RequireRMRole")]
[HttpPost("submit-bid")]
public async Task<IActionResult> SubmitBid([FromBody] BidRequest request)

// Multiple roles
[Authorize(Policy = "RequirePrivilegedAccess")] // Admin or Treasury
[HttpGet("admin/users")]
public async Task<IActionResult> GetAllUsers()

// Anonymous access
[AllowAnonymous]
[HttpPost("login")]
public async Task<IActionResult> Login([FromBody] LoginRequest request)
```

### Service Layer Design

**Service Interface:**
```csharp
public interface IUserAccountService
{
    Task<UserAccountDetails?> GetUserDetailsByAccountNoAsync(string accountNo);
    Task<KachasiDetails?> GetKachasiDetailsAsync(string formNumber);
    Task<Result> SubmitBidAsync(ApplicantDetails application, List<IFormFile> files);
}
```

**Service Implementation:**
```csharp
public class UserAccountService : IUserAccountService
{
    private readonly IUserAccountRepository _repository;
    private readonly ILogger<UserAccountService> _logger;

    public UserAccountService(
        IUserAccountRepository repository,
        ILogger<UserAccountService> logger)
    {
        _repository = repository;
        _logger = logger;
    }

    public async Task<UserAccountDetails?> GetUserDetailsByAccountNoAsync(string accountNo)
    {
        if (string.IsNullOrWhiteSpace(accountNo))
        {
            _logger.LogWarning("Account number is null or empty");
            return null;
        }

        try
        {
            return await _repository.GetDetailsByAccountNoAsync(accountNo);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving user details for account: {AccountNo}", accountNo);
            throw;
        }
    }
}
```

## Testing Guidelines

### Unit Testing

**Test Structure:**
```csharp
[TestClass]
public class UserAccountServiceTests
{
    private Mock<IUserAccountRepository> _mockRepository;
    private Mock<ILogger<UserAccountService>> _mockLogger;
    private UserAccountService _service;

    [TestInitialize]
    public void Setup()
    {
        _mockRepository = new Mock<IUserAccountRepository>();
        _mockLogger = new Mock<ILogger<UserAccountService>>();
        _service = new UserAccountService(_mockRepository.Object, _mockLogger.Object);
    }

    [TestMethod]
    public async Task GetUserDetailsByAccountNoAsync_ValidAccountNo_ReturnsUserDetails()
    {
        // Arrange
        var accountNo = "**********";
        var expectedDetails = new UserAccountDetails { AccountNumber = accountNo };
        _mockRepository.Setup(r => r.GetDetailsByAccountNoAsync(accountNo))
                      .ReturnsAsync(expectedDetails);

        // Act
        var result = await _service.GetUserDetailsByAccountNoAsync(accountNo);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(accountNo, result.AccountNumber);
        _mockRepository.Verify(r => r.GetDetailsByAccountNoAsync(accountNo), Times.Once);
    }

    [TestMethod]
    public async Task GetUserDetailsByAccountNoAsync_NullAccountNo_ReturnsNull()
    {
        // Act
        var result = await _service.GetUserDetailsByAccountNoAsync(null);

        // Assert
        Assert.IsNull(result);
        _mockRepository.Verify(r => r.GetDetailsByAccountNoAsync(It.IsAny<string>()), Times.Never);
    }
}
```

### Integration Testing

**API Integration Tests:**
```csharp
[TestClass]
public class UserAccountControllerIntegrationTests
{
    private WebApplicationFactory<Program> _factory;
    private HttpClient _client;

    [TestInitialize]
    public void Setup()
    {
        _factory = new WebApplicationFactory<Program>();
        _client = _factory.CreateClient();
    }

    [TestMethod]
    public async Task GetDetailsByAccountNo_ValidAccount_ReturnsOk()
    {
        // Arrange
        var accountNo = "**********";
        var token = await GetAuthTokenAsync();
        _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        // Act
        var response = await _client.GetAsync($"/api/useraccount/details/{accountNo}");

        // Assert
        Assert.AreEqual(HttpStatusCode.OK, response.StatusCode);
    }
}
```

## Git Workflow

### Branch Strategy

**Main Branches:**
- **main**: Production-ready code
- **develop**: Integration branch for features
- **release/x.x.x**: Release preparation
- **hotfix/x.x.x**: Critical fixes

**Feature Branches:**
- **feature/feature-name**: New features
- **bugfix/bug-description**: Bug fixes
- **task/task-description**: General tasks

### Commit Guidelines

**Commit Message Format:**
```
<type>(<scope>): <subject>

<body>

<footer>
```

**Types:**
- **feat**: New feature
- **fix**: Bug fix
- **docs**: Documentation changes
- **style**: Code style changes
- **refactor**: Code refactoring
- **test**: Adding tests
- **chore**: Maintenance tasks

**Examples:**
```
feat(auth): add JWT refresh token functionality

Implement refresh token mechanism to allow users to renew
expired JWT tokens without re-authentication.

Closes #123
```

### Pull Request Process

1. **Create Feature Branch**: `git checkout -b feature/new-feature`
2. **Implement Changes**: Follow coding standards
3. **Write Tests**: Ensure adequate test coverage
4. **Update Documentation**: Update relevant docs
5. **Create Pull Request**: Use PR template
6. **Code Review**: Address reviewer feedback
7. **Merge**: Squash and merge to develop

## Performance Guidelines

### Database Performance

**Query Optimization:**
```csharp
// Good - Use specific columns and filtering
var users = await _context.Users
    .Where(u => u.IsActive && u.RoleType == "RM")
    .Select(u => new { u.Id, u.Email, u.FullName })
    .ToListAsync();

// Bad - Select all columns and filter in memory
var users = await _context.Users.ToListAsync();
var activeRMs = users.Where(u => u.IsActive && u.RoleType == "RM");
```

**Async/Await Best Practices:**
```csharp
// Good - Proper async usage
public async Task<Result> ProcessApplicationAsync(ApplicantDetails application)
{
    var validation = await ValidateApplicationAsync(application);
    if (!validation.IsValid)
        return new ErrorResult(validation.Message);

    var result = await _repository.SaveAsync(application);
    await _emailService.SendNotificationAsync(application.RMEmail);
    
    return new SuccessResult { Content = result };
}

// Bad - Blocking async calls
public Result ProcessApplication(ApplicantDetails application)
{
    var validation = ValidateApplicationAsync(application).Result; // Blocks thread
    // ... rest of implementation
}
```

This developer guide provides comprehensive standards and practices for maintaining high-quality code in the BID Confirmation Portal API project.
