﻿namespace BidConfirmationAPI.Models
{
    public class SuccessResult : Result
    {
        public SuccessResult() : base(true)
        {
            Status = 200;
        }

        public SuccessResult(object content) : base(true, string.Empty, content)
        {
            Message = "Successful";
            Status = 200;
        }

        public SuccessResult(string message) : base(true, message, null)
        {
            Status = 200;
        }

        public SuccessResult(object content, string message) : base(true, message, content)
        {
            Status = 200;
        }

        public SuccessResult(object content, string message, int statusCode) : base(true, message, content)
        {
            Status = statusCode;
        }

    }
}
