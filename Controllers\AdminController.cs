﻿using BidConfirmationAPI.Data;
using BidConfirmationAPI.Interfaces;
using BidConfirmationAPI.Model;
using BidConfirmationAPI.Models;
using BidConfirmationAPI.Services;
using Mapster;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;

namespace BID_CONFIRMATION.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AdminController : ControllerBase
    {
        private readonly IAuthenticationService _authenticationService;
        private readonly IHttpContextAccessor _ctx;
        private readonly ApplicationDbContext _dbContext;
        private readonly SessionManager _sessionManager;
        private readonly IConfiguration _config;
        public AdminController(IAuthenticationService authenticationService, IHttpContextAccessor ctx, SessionManager sessionManager, ApplicationDbContext dbContext, IConfiguration config)
        {
            _authenticationService = authenticationService;
            _ctx = ctx;
            _dbContext = dbContext;
            _sessionManager = sessionManager;
            _config = config;
        }

        [Authorize(Policy = "RequirePrivilegedAccess")]
        [HttpPost("api/CreateUser")]
        public async Task<IActionResult> CreateUsers([FromBody] UserProfileCreate entity)
        {
            var result = await _authenticationService.CreateUser(entity);
            return Ok(result);
        }

        [AllowAnonymous]
        [HttpPost]
        [Route("api/Login")]
        public async Task<IActionResult> Login([FromBody] LoginCreate request)
        {
            try
            {

                var result = await _authenticationService.LoginAsync(request, IpAddress());
                

                return Ok(result);
            }
            catch (UnauthorizedAccessException ex)
            {

                return Unauthorized(new { Message = ex.Message });
            }

        }
        private string IpAddress()
        {
            if (Request.Headers.ContainsKey("X-Forwarded-For"))
                return Request.Headers["X-Forwarded-For"];
            else
                return HttpContext.Connection.RemoteIpAddress.MapToIPv4().ToString();
        }

        [AllowAnonymous]
        [HttpPost("logout")]
        public IActionResult Logout()
        {
            // End the user's session
            _sessionManager.EndSession();

            return Ok(new { message = "Logout successful" });
        }

        //[HttpPost]
        //[Route("api/ChangePassword")]
        //public async Task<IActionResult> ChangePassword([FromBody] PasswordChange passwordChange)
        //{
        //    try
        //    {

        //        var result = await _authenticationService.ChangePassword(passwordChange);

        //        return Ok(result);
        //    }
        //    catch (UnauthorizedAccessException ex)
        //    {

        //        return Unauthorized(new { Message = ex.Message });
        //    }

        //}

        //[HttpPost]
        //[Route("api/VerifyToken")]
        //public async Task<IActionResult> VerifyToken(TokenVerificationRequest request)
        //{
        //    try
        //    {

        //        if (_authenticationService.ValidateToken(request.Username, request.Token))
        //        {

        //            return Ok(new { Message = "Token verification successful. User is now authenticated." });
        //        }
        //        else
        //        {
        //            // If the token is invalid, return an error message
        //            return BadRequest(new { Message = "Invalid token." });
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        // Handle other exceptions
        //        return BadRequest(new { Message = ex.Message });
        //    }
        //}
        [AllowAnonymous]
        [HttpPost("verify-token")]
        public async Task<IActionResult> VerifyToken([FromBody] TokenVerificationRequest request)
        {
            try
            {
                var result = await _authenticationService.VerifyTokenAsync(request);

                if (result is SuccessResult success)
                {
                    return Ok(new
                    {
                        Message = success.Message,
                        Data = success.Content  // Includes Token, Email, FullName, and Role
                    });
                }

                return BadRequest(new { Message = result.Message });
            }
            catch (Exception ex)
            {
                AppLogger.LogError($"Token verification error: {ex.Message}");
                return StatusCode(500, new { Message = "An error occurred while verifying the token." });
            }
        }

        [Authorize(Policy = "RequirePrivilegedAccess")]
        [HttpPost]
        [Route("api/CreateRole")]
        public async Task<IActionResult> CreateRole(string roleName)
        {
            if (string.IsNullOrWhiteSpace(roleName))
            {
                return BadRequest("Role name cannot be empty");
            }

            var roleExists = await _dbContext.Roles.AnyAsync(r => r.RoleType == roleName);
            if (roleExists)
            {
                return Conflict("Role already exists");
            }

            var newRole = new Role { RoleType = roleName }; // Assuming Role is your entity class representing roles
            _dbContext.Roles.Add(newRole);
            await _dbContext.SaveChangesAsync();

            return Ok("Role created successfully");
        }

        [Authorize(Policy = "RequirePrivilegedAccess")]
        [HttpGet("GetAllUsers")]
        public async Task<IActionResult> GetAllUsers()
        {
            try
            {
                var result = await _dbContext.Users.ToListAsync();
                return Ok(result);
            }
            catch (Exception)
            {

                throw;
            }
        }

        [Authorize(Policy = "RequirePrivilegedAccess")]
        [HttpGet("GetFilteredUsers")]
        public async Task<IActionResult> GetAllUsers([FromQuery] UserQueryParameters queryParams)
        {
            try
            {
                var query = _dbContext.Users.AsQueryable();

                // Apply search filter (by FullName or Email)
                if (!string.IsNullOrWhiteSpace(queryParams.Search))
                {
                    string searchLower = queryParams.Search.ToLower();
                    query = query.Where(u => u.FullName.ToLower().Contains(searchLower) || u.Email.ToLower().Contains(searchLower));
                }

                // Filter by RoleType
                if (!string.IsNullOrWhiteSpace(queryParams.Role))
                {
                    query = query.Where(u => u.RoleType == queryParams.Role);
                }

                // Filter by IsActive
                if (queryParams.IsActive.HasValue)
                {
                    query = query.Where(u => u.IsActive == queryParams.IsActive.Value);
                }

                // Get total count before pagination
                int totalCount = await query.CountAsync();
                int totalPages = (int)Math.Ceiling(totalCount / (double)queryParams.PageSize);

                // Apply pagination
                var users = await query
                    .OrderBy(u => u.FullName) // Optional ordering
                    .Skip((queryParams.PageNumber - 1) * queryParams.PageSize)
                    .Take(queryParams.PageSize)
                    .ToListAsync();

                // Return paged result with metadata
                return Ok(new
                {
                    TotalCount = totalCount,
                    TotalPages = totalPages,
                    PageNumber = queryParams.PageNumber,
                    PageSize = queryParams.PageSize,
                    Users = users
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = ex.Message });
            }
        }



        [Authorize(Policy = "RequirePrivilegedAccess")]
        [HttpGet("GetAllRoles")]
        public async Task<IActionResult> GetAllRoles()
        {
            try
            {
                var result = await _dbContext.Roles.ToListAsync();
                return Ok(result);
            }
            catch (Exception)
            {

                throw;
            }
        }

        //[HttpGet("GetAllUserActivity")]
        //public async Task<IActionResult> GetAllUserActivity()
        //{
        //    try
        //    {
        //        var result = await _dbContext.UserActivities.ToListAsync();
        //        return Ok(result);
        //    }
        //    catch (Exception)
        //    {

        //        throw;
        //    }
        //}
        [Authorize(Policy = "RequirePrivilegedAccess")]
        [HttpGet("GetAllUserActivity")]
        public async Task<IActionResult> GetAllUserActivity(
    [FromQuery] string? userName = null,
    [FromQuery] string? actionType = null,
    [FromQuery] string? ipAddress = null,
    [FromQuery] DateTime? from = null,
    [FromQuery] DateTime? to = null,
    [FromQuery] int pageNumber = 1,
    [FromQuery] int pageSize = 10)
        {
            try
            {
                var query = _dbContext.UserActivities.AsQueryable();

                if (!string.IsNullOrWhiteSpace(userName))
                    query = query.Where(a => a.UserName!.ToLower().Contains(userName.ToLower()));

                if (!string.IsNullOrWhiteSpace(actionType))
                    query = query.Where(a => a.ActionType!.ToLower().Contains(actionType.ToLower()));

                if (!string.IsNullOrWhiteSpace(ipAddress))
                    query = query.Where(a => a.IpAddress.ToLower().Contains(ipAddress.ToLower()));

                if (from.HasValue)
                    query = query.Where(a => a.Timestamp >= from.Value);

                if (to.HasValue)
                    query = query.Where(a => a.Timestamp <= to.Value);

                var totalCount = await query.CountAsync();
                var totalPages = (int)Math.Ceiling(totalCount / (double)pageSize);

                var results = await query
                    .OrderByDescending(a => a.Timestamp)
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                return Ok(new
                {
                    TotalCount = totalCount,
                    TotalPages = totalPages,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    Activities = results
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = ex.Message });
            }
        }

        [Authorize(Policy = "RequirePrivilegedAccess")]
        [HttpGet("GetUserByEmail")]
        public async Task<IActionResult> GetUserByEmail(string email)
        {
            try
            {
                var result = await _authenticationService.GetUserByEmailAsync(email);
                return Ok(result);
            }
            catch (Exception)
            {

                throw;
            }
        }

        [Authorize(Policy = "RequirePrivilegedAccess")]
        [HttpDelete("DeleteUser")]
        public async Task<IActionResult> DeleteUser(string email)
        {
            try
            {
                var userToDelete = await _authenticationService.GetUserByEmailAsync(email);

                if (userToDelete != null)
                {
                    _dbContext.Users.Remove(userToDelete);
                    _dbContext.SaveChanges();
                    return Ok("User deleted successfully");
                }
                return NoContent();
            }
            catch (Exception)
            {

                throw;
            }
        }

        [Authorize(Policy = "RequirePrivilegedAccess")]
        [HttpPut("DeactivateUser")]
        public async Task<IActionResult> DeactivateUser(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return BadRequest("Email is required");

            try
            {
                var user = await _authenticationService.GetUserByEmailAsync(email);

                if (user == null)
                    return NotFound("User not found");

                if (!user.IsActive)
                    return BadRequest("User is already deactivated");

                user.IsActive = false;

                try
                {
                    await _authenticationService.UpdateUserAsync(user.Adapt<UserProfileUpdate>());
                    return Ok("User deactivated successfully");
                }
                catch (DbUpdateConcurrencyException)
                {
                    // Reload and retry
                    var latestUser = await _authenticationService.GetUserByEmailAsync(email);
                    if (latestUser == null)
                        return NotFound("User not found after concurrency check");

                    latestUser.IsActive = false;
                    await _authenticationService.UpdateUserAsync(latestUser.Adapt<UserProfileUpdate>());
                    return Ok("User deactivated successfully after retry");
                }
            }
            catch (Exception ex)
            {
                // TODO: log exception `ex` here
                return StatusCode(500, "Internal server error");
            }
        }


        //[HttpPut("UpdateUserStatus")]
        //public async Task<IActionResult> UpdateUserStatus(string email, [FromBody] bool IsActive)
        //{
        //    try
        //    {
        //        var userToUpdate = await _authenticationService.GetUserByEmailAsync(email);

        //        if (userToUpdate != null)
        //        {
        //            // Update the user's IsActive property based on the request payload
        //            userToUpdate.IsActive = IsActive;

        //            // Attempt to update the user in the database
        //            await _authenticationService.UpdateUserAsync(userToUpdate);

        //            return Ok($"User {(IsActive ? "activated" : "deactivated")} successfully");
        //        }
        //        else
        //        {
        //            return NotFound("User not found");
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        // Log the error or handle it appropriately
        //        return StatusCode(500, "Internal server error");
        //    }
        //}



        //[HttpPut("UpdateUser")]
        //public async Task<IActionResult> UpdateUser(UserProfileUpdate profileUpdate)
        //{
        //    try
        //    {
        //        if (ModelState.IsValid)
        //        {

        //            var existingUser = await _authenticationService.UpdateUserAsync(profileUpdate);

        //            return Ok("User updated successfully");
        //        }
        //        else
        //        {
        //            return BadRequest("Invalid model state");
        //        }
        //    }
        //    catch (Exception)
        //    {

        //        throw;
        //    }
        //}
        [Authorize(Policy = "RequirePrivilegedAccess")]
        [HttpPatch("update-user")]
        public async Task<IActionResult> UpdateUser([FromBody] UserProfileUpdate profileUpdate)
        {
            try
            {
                var result = await _authenticationService.UpdateUserAsync(profileUpdate);

                if (result is SuccessResult success)
                {
                    return Ok(success);
                }
                else if (result is ErrorResult error)
                {
                    return BadRequest(error);
                }

                return BadRequest(new { Message = "Unexpected response from the service." });
            }
            catch (Exception ex)
            {
                AppLogger.LogError($"User update failed: {ex.Message}");
                return StatusCode(500, new { Message = "An error occurred while updating user details." });
            }
        }

        [AllowAnonymous]
        [HttpPost("refresh")]
        public async Task<IActionResult> Refresh([FromBody] string refreshToken, [FromServices] IHttpContextAccessor httpContextAccessor)
        {
            var ipAddress = httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString();

            var storedToken = await _dbContext.RefreshTokens
                .FirstOrDefaultAsync(rt => rt.Token == refreshToken);

            if (storedToken == null || !storedToken.IsActive)
                return Unauthorized("Invalid refresh token");

            var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Email == storedToken.CreatedByIp);
            if (user == null)
                return Unauthorized("User not found");

            // Replace old refresh token
            storedToken.Revoked = DateTime.UtcNow;
            storedToken.RevokedByIp = ipAddress;
            var newRefreshToken = GenerateRefreshToken(ipAddress);
            storedToken.ReplacedByToken = newRefreshToken.Token;

            _dbContext.RefreshTokens.Add(newRefreshToken);

            var newJwt = generateJwtToken(user.Adapt<UserProfileModel>());

            await _dbContext.SaveChangesAsync();

            return Ok(new
            {
                AccessToken = newJwt,
                RefreshToken = newRefreshToken.Token
            });
        }

        [AllowAnonymous]
        [HttpPost("revoke")]
        public async Task<IActionResult> Revoke([FromBody] string refreshToken, [FromServices] IHttpContextAccessor httpContextAccessor)
        {
            var ipAddress = httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString();

            var storedToken = await _dbContext.RefreshTokens
                .FirstOrDefaultAsync(rt => rt.Token == refreshToken);

            if (storedToken == null || !storedToken.IsActive)
                return NotFound("Token not found or already inactive");

            storedToken.Revoked = DateTime.UtcNow;
            storedToken.RevokedByIp = ipAddress;

            await _dbContext.SaveChangesAsync();
            return Ok("Token revoked");
        }


        private RefreshToken GenerateRefreshToken(string ipAddress)
        {
            return new RefreshToken
            {
                Token = Convert.ToBase64String(RandomNumberGenerator.GetBytes(64)), // cryptographically secure
                Expires = DateTime.UtcNow.AddDays(7), // valid for 7 days
                Created = DateTime.UtcNow,
                CreatedByIp = ipAddress
            };
        }

        // private string generateJwtToken(UserProfileModel user)
        private string generateJwtToken(UserProfileModel user)
        {
            string appKey = _config["JwtSettings:SecretKey"];
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(appKey);

            var claims = new[]
            {
        new Claim("UserId", user.Id.ToString()),
        new Claim("Email", user.Email),
        new Claim("Name", user.FullName),
        new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
    };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddMinutes(5),
                Audience = _config["JwtSettings:Audience"],
                Issuer = _config["JwtSettings:Issuer"],
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }



    }
}
