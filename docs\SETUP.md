# Setup and Installation Guide

## Prerequisites

### Software Requirements
- **.NET 6 SDK** (6.0 or later)
- **SQL Server** (2019 or later) or **SQL Server Express**
- **Visual Studio 2022** or **Visual Studio Code**
- **Git** for version control
- **Postman** or similar API testing tool (optional)

### System Requirements
- **Windows 10/11** or **Windows Server 2019/2022**
- **Minimum 4GB RAM** (8GB recommended)
- **Minimum 10GB free disk space**
- **Network access** to LDAP server and external databases

### Access Requirements
- **LDAP Server Access**: For staff authentication
- **SQL Server Access**: Database creation and management permissions
- **Oracle Database Access**: For external account queries (if applicable)
- **Email Service Access**: For notification functionality

## Installation Steps

### 1. Clone the Repository

```bash
git clone <repository-url>
cd BidConfirmationAPI
```

### 2. Install .NET 6 SDK

Download and install from [Microsoft .NET Downloads](https://dotnet.microsoft.com/download/dotnet/6.0)

Verify installation:
```bash
dotnet --version
```

### 3. Database Setup

#### SQL Server Installation
1. Download SQL Server Developer Edition or SQL Server Express
2. Install with default settings
3. Enable SQL Server Authentication (Mixed Mode)
4. Note the server instance name (e.g., `localhost\SQLEXPRESS`)

#### Create Database
```sql
-- Connect to SQL Server Management Studio
-- Create new database
CREATE DATABASE BID_CONFIRMATION;
```

### 4. Configuration

#### Update Connection Strings
Edit `appsettings.json`:

```json
{
  "ConnectionStrings": {
    "BID_CONFIRMATIONDB": "server=YOUR_SERVER;Database=BID_CONFIRMATION;uid=YOUR_USERNAME;password=YOUR_PASSWORD;TrustServerCertificate=True",
    "NIBSSDB": "server=YOUR_NIBSS_SERVER;Database=NIBBS_INWARD;uid=YOUR_USERNAME;password=YOUR_PASSWORD;",
    "AccountQuery": "select CUSTOM.effbal(acid,'01') as avail_bal,custom.getbvn(foracid) as bvn,lien_amt,acct_name from tbaadm.gam where foracid = :AccountNo"
  }
}
```

#### Configure LDAP Settings
Update LDAP configuration in `appsettings.json`:

```json
{
  "ExternalUrl": {
    "LDapServerIP": "YOUR_LDAP_SERVER_IP",
    "LDapServerPort": "389"
  }
}
```

#### Configure Email Settings
Update email service configuration:

```json
{
  "ExternalUrl": {
    "EmailApiBaseUrl": "YOUR_EMAIL_API_URL",
    "SendEmail": "/api/SendEmail"
  },
  "Settings": {
    "Sender": "<EMAIL>"
  }
}
```

#### JWT Configuration
Update JWT settings (generate a secure secret key):

```json
{
  "JwtSettings": {
    "SecretKey": "your-256-bit-secret-key-here",
    "Issuer": "your-app-name",
    "Audience": "your-app-users",
    "ExpiryMinutes": 30
  }
}
```

### 5. Database Migration

#### Install Entity Framework Tools
```bash
dotnet tool install --global dotnet-ef
```

#### Run Migrations
```bash
# Navigate to project directory
cd BidConfirmationAPI

# Update database with migrations
dotnet ef database update
```

#### Verify Database Schema
Check that the following tables are created:
- Users
- ApplicantDetails
- Documents
- Tokens
- UserActivities
- AuditTrails
- Roles
- DocumentDownloads
- RefreshTokens
- FailedLoginAttempts

### 6. Logging Configuration

#### Create Log Directory
```bash
# Create logging directory
mkdir C:\Logs\BIDCONFIRMATION
```

#### Verify NLog Configuration
Check `nlog.config` file exists and points to correct log directory:

```xml
<target name="logfile" xsi:type="File"
        fileName="C:\Logs\BIDCONFIRMATION\${shortdate}_logfile.txt"
        layout="${longdate} ${level:uppercase=true} ${message}"/>
```

### 7. Build and Run

#### Restore Dependencies
```bash
dotnet restore
```

#### Build Project
```bash
dotnet build
```

#### Run Application
```bash
dotnet run
```

The application will start on:
- **HTTP**: `http://localhost:5056`
- **HTTPS**: `https://localhost:7011`

### 8. Verify Installation

#### Access Swagger UI
Navigate to: `https://localhost:7011/swagger`

#### Test Health Check
```bash
curl https://localhost:7011/weatherforecast
```

## Development Environment Setup

### Visual Studio 2022 Setup
1. Open `BidConfirmationAPI.sln`
2. Set `BidConfirmationAPI` as startup project
3. Configure launch settings in `Properties/launchSettings.json`
4. Press F5 to run with debugging

### Visual Studio Code Setup
1. Install C# extension
2. Open project folder
3. Configure `.vscode/launch.json` and `.vscode/tasks.json`
4. Use Ctrl+F5 to run

### Package Management
```bash
# Add new package
dotnet add package PackageName

# Update packages
dotnet restore

# List packages
dotnet list package
```

## Initial Data Setup

### Create Admin User
Use the API endpoint to create the first admin user:

```bash
POST /api/CreateUser
{
  "fullName": "System Administrator",
  "email": "<EMAIL>",
  "roleType": "Admin",
  "isActive": true
}
```

### Create User Roles
Insert initial roles into the database:

```sql
INSERT INTO Roles (Id, Name, Description) VALUES 
(NEWID(), 'Admin', 'System Administrator'),
(NEWID(), 'RM', 'Relationship Manager'),
(NEWID(), 'TRADE', 'Trade Team Member'),
(NEWID(), 'TREASURY', 'Treasury Team Member');
```

## Environment-Specific Configuration

### Development Environment
- Use local SQL Server instance
- Enable detailed logging
- Use development LDAP server
- Disable HTTPS redirection for testing

### Staging Environment
- Use staging database
- Configure staging LDAP
- Enable production-like logging
- Test email notifications

### Production Environment
- Use production database with clustering
- Configure production LDAP
- Enable comprehensive logging
- Set up monitoring and alerting

## Troubleshooting Common Issues

### Database Connection Issues
1. Verify SQL Server is running
2. Check connection string format
3. Ensure database exists
4. Verify user permissions

### LDAP Authentication Issues
1. Check LDAP server connectivity
2. Verify LDAP configuration
3. Test with known user credentials
4. Check firewall settings

### Migration Issues
1. Ensure database exists
2. Check Entity Framework tools installation
3. Verify connection string
4. Run migrations with verbose output

### Port Conflicts
1. Check if ports 5056/7011 are available
2. Update `launchSettings.json` if needed
3. Configure firewall exceptions

## Security Considerations

### Development Security
- Use development certificates for HTTPS
- Keep sensitive data in user secrets
- Use development LDAP accounts
- Enable detailed error messages

### Production Security
- Use production SSL certificates
- Store secrets in secure configuration
- Implement proper LDAP security
- Disable detailed error messages
- Enable audit logging
- Configure CORS properly

## Next Steps

After successful installation:
1. Review [API Documentation](API.md)
2. Understand [Authentication & Security](SECURITY.md)
3. Learn about [Business Workflows](WORKFLOWS.md)
4. Set up [Development Environment](DEVELOPMENT.md)

For ongoing maintenance, refer to the [Configuration Guide](CONFIGURATION.md) and [Troubleshooting Guide](TROUBLESHOOTING.md).
