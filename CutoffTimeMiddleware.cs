﻿namespace BIDConfirmation
{
    public class CutoffTimeMiddleware
    {
        private readonly RequestDelegate _next;

        public CutoffTimeMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (context.User.Identity.IsAuthenticated)
            {
                var role = context.User.Claims.FirstOrDefault(c => c.Type == "RoleType")?.Value;
                var currentTime = DateTime.Now; 

                if ((role == "RM" || role == "TRADE") && currentTime.TimeOfDay > new TimeSpan(15, 0, 0))
                {
                    context.Response.StatusCode = StatusCodes.Status403Forbidden;
                    await context.Response.WriteAsync("Access denied: Cut-off time (3:00 PM) reached.");
                    return;
                }
            }

            await _next(context);
        }
    }

}
