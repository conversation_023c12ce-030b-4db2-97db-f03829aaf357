using BIDConfirmation.Model;
using BidConfirmationAPI.Data;
using BidConfirmationAPI.Interfaces;
using BidConfirmationAPI.Model;
using Microsoft.Identity.Client;

namespace BidConfirmationAPI.Services
{
    public class UserAccountService : IUserAccountService
    {
        private readonly IUserAccountRepository _userAccountRepository;
        private readonly ILogger<UserAccountService> _logger;
        private readonly ApplicationDbContext _context;

        public UserAccountService(
            IUserAccountRepository userAccountRepository,
            ILogger<UserAccountService> logger, ApplicationDbContext context)
        {
            _userAccountRepository = userAccountRepository ?? throw new ArgumentNullException(nameof(userAccountRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _context = context;
        }

        public async Task<KachasiDetails?> GetKachasiDetailsAsync(string FormNumber)
        {
            if (string.IsNullOrWhiteSpace(FormNumber))
            {
                _logger.LogWarning("Form number is null or empty");
                return null;
            }

            try
            {
                var result = await _userAccountRepository.GetKachasiDetailsAsync(FormNumber);

                if (result == null)
                {
                    _logger.LogInformation("No user details found for form number: {formNo}", FormNumber);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user details for form number: {formNo}", FormNumber);
                throw;
            }
        }

        //public async Task<KachasiDetails?> GetKachasiDetailAsync(string PickupListID, string FormNumber)
        //{
        //    if (string.IsNullOrWhiteSpace(FormNumber))
        //    {
        //        _logger.LogWarning("Form number is null or empty");
        //        return null;
        //    }
        //    if (string.IsNullOrWhiteSpace(PickupListID))
        //    {
        //        _logger.LogWarning("PickupList ID is null or empty");
        //        return null;
        //    }

        //    try
        //    {
        //        var result = await _userAccountRepository.GetKachasiDetailAsync(PickupListID, FormNumber);

        //        if (result == null)
        //        {
        //            _logger.LogInformation("No user details found for form number: {formNo}", FormNumber);
        //        }

        //        return result;
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "Error retrieving user details for form number: {formNo}", FormNumber);
        //        throw;
        //    }
        //}

        public async Task<UserAccountDetails?> GetUserDetailsByAccountNoAsync(string accountNo)
        {
            if (string.IsNullOrWhiteSpace(accountNo))
            {
                _logger.LogWarning("Account number is null or empty");
                return null;
            }

            try
            {
                var result = await _userAccountRepository.GetDetailsByAccountNoAsync(accountNo);
                
                if (result == null)
                {
                    _logger.LogInformation("No user details found for account number: {AccountNo}", accountNo);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving user details for account number: {AccountNo}", accountNo);
                throw;
            }
        }



        //public async Task SaveBidRequestAsync(BidRequest bidRequest)
        //{
        //    var applicant = new ApplicantDetails
        //    {
        //        ApplicantName = bidRequest.formMInfo.applicantName,
        //        FormMNumber = bidRequest.formMInfo.formMNumber,
        //        TransactionFileRef = bidRequest.formMInfo.fundApplicationId,
        //        PurchaseAmountUSD = bidRequest.rateInfo.amount,
        //        GeneralGoodsDescription = bidRequest.formMInfo.generalGoodsDescription,
        //        PaymentModeOrFundAppID = bidRequest.formMInfo.paymentMode,
        //        SourcesOfFunds = "Not specified", // Set this if available elsewhere
        //        CustomerAccount = bidRequest.accountInfo.accountNumber,
        //        BVNNumber = bidRequest.accountInfo.bvn,
        //        Rate = bidRequest.rateInfo.rate,
        //        ValueDate = DateTime.Now, // Or another appropriate date
        //        Status = "Pending",
        //        PurchaseAmountFormMCurrency = bidRequest.formMInfo.totalCFValue,
        //        ApplicantAccountName = bidRequest.accountInfo.accountName,
        //        ApplicantAddress = bidRequest.formMInfo.applicantAddress,
        //        BeneficiaryName = bidRequest.formMInfo.beneficiaryName,
        //        ApplicantTinNumber = bidRequest.formMInfo.applicantTin,
        //        RCNumber = bidRequest.formMInfo.rcNumber,
        //        ApplicantPhoneNumber = bidRequest.formMInfo.applicantPhone,
        //        CountryOfSupply = bidRequest.formMInfo.countryOfSupply,
        //        HSCode = bidRequest.formMInfo.hsCode,
        //        Currency = bidRequest.formMInfo.currency,
        //        TotalCNFValue = bidRequest.formMInfo.totalCFValue,
        //        FormMRegistrationDate = bidRequest.formMInfo.formMRegistrationDate,
        //        Value110PercentCNF = bidRequest.rateInfo.calculatedValue,
        //        AvailableBalance = bidRequest.accountInfo.accountBalance,
        //        Comments = "" // Add if needed
        //    };

        //    //using var context = new YourDbContext(); // Replace with DI if available
        //    _context.ApplicantDetails.Add(applicant);
        //    await _context.SaveChangesAsync();
        //}


    }
}