﻿namespace BIDConfirmation.Model
{
    public class KachasiDetails
    {
        public string APPLICANT_NAME { get; set; }
        public string FORM_NUMBER { get; set; }
       
       // public string PICKLIST_ID { get; set; }
        public string APPLICATION_NUMBER { get; set; }
        public decimal TOT_FOB_VAL_AMT { get; set; }
        public string GOODS_DESCRIPTION { get; set; }
        public string PAYMENT_MODE { get; set; }
        public string Payment_Description { get; set; }
        public string FUNDS_SOURCE { get; set; }
        public string ACCOUNT_NUMBER { get; set; }
        public decimal EXCHANGE_RATE { get; set; }
        public decimal TOT_CNF_VAL_AMT { get; set; }
        public DateTime SUBMISSION_DATE { get; set; }
        public string APPLICANT_RCNUMBER { get; set; }
        public string APPLICANT_PHONE { get; set; }
        public string COUNTRY_OF_SUPPLY { get; set; }
        public string TOT_CNF_VAL_CURR { get; set; }
        public DateTime CREATED_AT { get; set; }
        public string APPLICANT_ADDRESS { get; set; }
        public string BENEFICIARY_NAME { get; set; }
        public string APPLICANT_TIN { get; set; }
    }
}
