﻿using BIDConfirmation;
using BIDConfirmation.Services;
using BidConfirmationAPI.Data;
using BidConfirmationAPI.Interfaces;
using BidConfirmationAPI.Repositories;
using BidConfirmationAPI.Services;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using System.Data;
using System.Security.Claims;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
//var jwtSettings = builder.Configuration.GetSection("JwtSettings");
//var secretKey = Encoding.UTF8.GetBytes(jwtSettings["SecretKey"]);

//builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
//    .AddJwtBearer(options =>
//    {
//        options.TokenValidationParameters = new TokenValidationParameters
//        {
//            ValidateIssuer = true,
//            ValidateAudience = true,
//            ValidateLifetime = true,
//            ValidateIssuerSigningKey = true,
//            ValidIssuer = jwtSettings["Issuer"],
//            ValidAudience = jwtSettings["Audience"],
//            IssuerSigningKey = new SymmetricSecurityKey(secretKey)
//        };
//    });

var key = Encoding.ASCII.GetBytes(builder.Configuration["JwtSettings:SecretKey"]);

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = builder.Configuration["JwtSettings:Issuer"],
        ValidAudience = builder.Configuration["JwtSettings:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(builder.Configuration["JwtSettings:SecretKey"])
        ),
        RoleClaimType = ClaimTypes.Role // 👈 make sure roles bind
    };
});


builder.Services.AddAuthorization();

builder.Services.AddControllers();

builder.Services.AddCors(options =>
{
    //options.AddPolicy("AllowSpecificOrigins", policy =>
    //{
    //    policy.WithOrigins("https://yourfrontenddomain.com") // Replace with your actual frontend URL
    //          .AllowAnyHeader()
    //          .AllowAnyMethod()
    //          .AllowCredentials(); // Only if authentication is required
    //});

    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle

builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();
builder.Services.AddScoped<SessionManager>();
builder.Services.AddHttpContextAccessor();

builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("BID_CONFIRMATIONDB")));

builder.Services.AddScoped<IDbConnection>(provider => new SqlConnection(builder.Configuration.GetConnectionString("BID_CONFIRMATIONDB")));
builder.Services.AddScoped<IUserAccountRepository, UserAccountRepository>();
builder.Services.AddScoped<IUserAccountService, UserAccountService>();
builder.Services.AddScoped<EmailService>();
builder.Services.AddScoped<IAuthorizationHandler, RoleHandler>();
// Example: Startup.cs / Program.cs
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy("RequireAdminRole",
         policy => policy.Requirements.Add(new RoleRequirement("Admin")));
    options.AddPolicy("RequireTreasuryRole", policy =>
        policy.Requirements.Add(new RoleRequirement("Treasury")));
    options.AddPolicy("RequireTradeRole",
        policy => policy.Requirements.Add(new RoleRequirement("Trade")));
    options.AddPolicy("RequireRMRole", policy =>
        policy.Requirements.Add(new RoleRequirement("RM")));
    options.AddPolicy("RequireSGCRole", policy =>
        policy.Requirements.Add(new RoleRequirement("SGC")));
    options.AddPolicy("RequireAuditorRole", policy =>
        policy.Requirements.Add(new RoleRequirement("Auditor")));
    options.AddPolicy("RequirePrivilegedAccess", policy =>
    policy.Requirements.Add(new RoleRequirement("Admin", "SGC", "Auditor")));
});


builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo { Title = "Bid Confirmation", Version = "v1" });

    // Add Bearer Authorization
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        In = ParameterLocation.Header,
        Description = "JWT Authorization header using the Bearer scheme. Example: \"Bearer {token}\"",
        Name = "Authorization",
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
{
    {
        new OpenApiSecurityScheme
        {
            Reference = new OpenApiReference
            {
                Type = ReferenceType.SecurityScheme,
                Id = "Bearer"
            },
            Scheme = "oauth2",
            Name = "Bearer",
            In = ParameterLocation.Header,
        },
        new List<string>()
    }
});

});


var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}
app.UseSwagger();
app.UseSwaggerUI();
app.UseCors("AllowAll");
app.UseHttpsRedirection();

app.UseAuthentication();

app.UseMiddleware<CutoffTimeMiddleware>();

app.UseAuthorization();

app.MapControllers();

app.Run();
