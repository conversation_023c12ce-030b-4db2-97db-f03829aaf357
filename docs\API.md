# API Documentation

## Overview

The BID Confirmation Portal API provides RESTful endpoints for managing Form M applications, user authentication, and administrative functions. All endpoints return JSON responses and require appropriate authentication and authorization.

## Base URL

- **Development**: `https://localhost:7011`
- **Production**: `https://your-production-domain.com`

## Authentication

### Bearer Token Authentication

All protected endpoints require a JWT Bearer token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Authentication Flow

1. **Login**: `POST /api/Login` - Get authentication token
2. **Token Verification**: `POST /api/VerifyToken` - Verify 2FA token and get JWT
3. **Refresh Token**: `POST /api/RefreshToken` - Refresh expired JWT

## API Endpoints

### Authentication Endpoints

#### Login
```http
POST /api/Login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "userpassword"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Authentication token sent to your email",
  "status": 200
}
```

#### Verify Token
```http
POST /api/VerifyToken
Content-Type: application/json

{
  "username": "<EMAIL>",
  "token": "123456"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "content": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "base64-encoded-refresh-token",
    "success": true,
    "email": "<EMAIL>"
  }
}
```

### User Management Endpoints (Admin Only)

#### Create User
```http
POST /api/CreateUser
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "fullName": "John Doe",
  "email": "<EMAIL>",
  "roleType": "RM",
  "isActive": true
}
```

**Authorization**: `RequirePrivilegedAccess` (Admin/Treasury)

#### Update User
```http
PATCH /api/update-user
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "id": "user-guid",
  "fullName": "Updated Name",
  "email": "<EMAIL>",
  "roleType": "TRADE",
  "isActive": true
}
```

#### Get All Users
```http
GET /api/GetAllUsers?pageNumber=1&pageSize=10&searchTerm=john
Authorization: Bearer <admin-token>
```

**Query Parameters:**
- `pageNumber` (optional): Page number for pagination
- `pageSize` (optional): Number of items per page
- `searchTerm` (optional): Search filter for user names/emails

#### Create Role
```http
POST /api/CreateRole?roleName=NewRole
Authorization: Bearer <admin-token>
```

### Account Information Endpoints (RM Only)

#### Get Account Details
```http
GET /api/UserAccount/details/{accountNo}
Authorization: Bearer <rm-token>
```

**Authorization**: `RequireRMRole`

**Response:**
```json
{
  "accountNumber": "**********",
  "accountName": "Customer Name",
  "bvn": "**********1",
  "availableBalance": 1000000.00
}
```

#### Get Kachasi Details
```http
GET /api/UserAccount/kachasiDetails/{FormNumber}
Authorization: Bearer <rm-token>
```

**Authorization**: `RequireRMRole`

### Bid Management Endpoints

#### Submit Bid Application
```http
POST /api/UserAccount/submit-bid
Authorization: Bearer <rm-token>
Content-Type: multipart/form-data

applicantJson: {
  "formMNumber": "FM123456",
  "applicantName": "Company Name",
  "applicantAddress": "Company Address",
  "beneficiaryName": "Beneficiary Name",
  "applicantTin": "********",
  "rcNumber": "RC123456",
  "applicantPhone": "+234********9",
  "countryOfSupply": "China",
  "currency": "USD",
  "totalCFValue": 100000.00,
  "formMRegistrationDate": "2024-01-15T00:00:00Z",
  "generalGoodsDescription": "Electronics",
  "paymentMode": "LC",
  "accountNumber": "**********",
  "accountName": "Account Name",
  "bvn": "**********1",
  "availableBalance": 1000000.00,
  "purchaseAmountUSD": 95000.00,
  "rate": 1500.00,
  "tolerance": 2.5,
  "purchaseAmountInFormMCurrency": 95000.00,
  "valueDate": "2024-02-15T00:00:00Z"
}
RMEmail: <EMAIL>
files: [file1.pdf, file2.pdf]
```

**Authorization**: `RequireRMRole`

#### Get All Applications
```http
GET /api/UserAccount/get-all-requests?pageNumber=1&pageSize=10&status=Submitted
Authorization: Bearer <token>
```

**Query Parameters:**
- `pageNumber` (optional): Page number
- `pageSize` (optional): Items per page
- `status` (optional): Filter by status

#### Get Application by ID
```http
GET /api/UserAccount/get-request/{id}
Authorization: Bearer <token>
```

### Trade Review Endpoints (Trade Team Only)

#### Trade Review
```http
POST /api/UserAccount/trade-review/{id}
Authorization: Bearer <trade-token>
Content-Type: application/json

{
  "approved": true,
  "reviewer": "<EMAIL>",
  "transactionReference": "TXN********9",
  "reason": "Rejection reason if not approved"
}
```

**Authorization**: `RequireTradeRole`

### Treasury Endpoints (Treasury Only)

#### Treasury Review
```http
POST /api/UserAccount/treasury-review/{id}
Authorization: Bearer <treasury-token>
Content-Type: application/json

{
  "approved": true,
  "reviewer": "<EMAIL>",
  "reason": "Rejection reason if not approved",
  "amount": 95000.00,
  "rate": 1500.00,
  "valueDate": "2024-02-15T00:00:00Z"
}
```

**Authorization**: `RequireTreasuryRole`

### Document Management Endpoints

#### Download Document
```http
GET /api/UserAccount/download-document/{documentId}
Authorization: Bearer <token>
```

**Response**: Binary file content with appropriate headers

#### Get Downloaded Documents
```http
GET /api/UserAccount/downloaded-documents?pageNumber=1&pageSize=10
Authorization: Bearer <treasury-token>
```

**Authorization**: `RequireTreasuryRole`

### Audit and Monitoring Endpoints (Admin Only)

#### Get User Activities
```http
GET /api/GetAllUserActivity?userName=<EMAIL>&actionType=POST&from=2024-01-01&to=2024-12-31&pageNumber=1&pageSize=10
Authorization: Bearer <admin-token>
```

**Query Parameters:**
- `userName` (optional): Filter by username
- `actionType` (optional): Filter by action type
- `ipAddress` (optional): Filter by IP address
- `from` (optional): Start date filter
- `to` (optional): End date filter
- `pageNumber` (optional): Page number
- `pageSize` (optional): Items per page

**Authorization**: `RequirePrivilegedAccess`

## Data Models

### User Profile Create
```json
{
  "fullName": "string",
  "email": "string",
  "roleType": "Admin|RM|TRADE|TREASURY",
  "isActive": true
}
```

### Applicant Details
```json
{
  "formMNumber": "string",
  "applicantName": "string",
  "applicantAddress": "string",
  "beneficiaryName": "string",
  "applicantTin": "string",
  "rcNumber": "string",
  "applicantPhone": "string",
  "countryOfSupply": "string",
  "hsCode": "string",
  "currency": "string",
  "totalCFValue": 0.0,
  "formMRegistrationDate": "2024-01-01T00:00:00Z",
  "generalGoodsDescription": "string",
  "paymentMode": "string",
  "accountNumber": "string",
  "accountName": "string",
  "bvn": "string",
  "availableBalance": 0.0,
  "purchaseAmountUSD": 0.0,
  "rate": 0.0,
  "tolerance": 0.0,
  "purchaseAmountInFormMCurrency": 0.0,
  "valueDate": "2024-01-01T00:00:00Z"
}
```

### Approval Request
```json
{
  "approved": true,
  "reviewer": "string",
  "transactionReference": "string",
  "reason": "string"
}
```

### Treasury Approval Request
```json
{
  "approved": true,
  "reviewer": "string",
  "reason": "string",
  "amount": 0.0,
  "rate": 0.0,
  "valueDate": "2024-01-01T00:00:00Z"
}
```

## Status Codes and Error Handling

### HTTP Status Codes
- `200 OK`: Successful request
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

### Error Response Format
```json
{
  "message": "Error description",
  "status": 400,
  "errors": ["Detailed error messages"]
}
```

## Rate Limiting

- **Failed Login Attempts**: Maximum 5 attempts per hour per email/IP
- **Account Lockout**: 1 hour after exceeding failed attempts

## Business Rules

### Time Restrictions
- **RM and TRADE roles**: Access restricted after 3:00 PM
- **Treasury and Admin**: No time restrictions

### Workflow Status
- `Submitted`: Initial submission by RM
- `TradeAccepted`: Approved by Trade team
- `TradeRejected`: Rejected by Trade team
- `TreasuryAccepted`: Final approval by Treasury
- `TreasuryRejected`: Rejected by Treasury

## Testing with Swagger

Access the interactive API documentation at:
- **Development**: `https://localhost:7011/swagger`
- **Production**: `https://your-domain.com/swagger`

The Swagger UI provides:
- Interactive endpoint testing
- Request/response examples
- Authentication testing
- Schema documentation

## Postman Collection

Import the API endpoints into Postman for testing:

1. Create new collection
2. Set base URL variable
3. Configure Bearer token authentication
4. Add environment variables for different environments

This API documentation provides comprehensive coverage of all available endpoints and their usage patterns for the BID Confirmation Portal system.
