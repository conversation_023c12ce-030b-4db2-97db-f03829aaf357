﻿namespace BidConfirmationAPI.Models
{
    public class UserProfileCreate
    {
        public string FullName { get; set; }

        public string Email { get; set; }


        //public string? FintechName { get; set; }


        //public string PrimaryAccountNum { get; set; }

        //public string AccountEquivalent { get; set; }
        //public string AccountType { get; set; }
        //public string Password { get; set; }

       // public bool IsInternal { get; set; }

        public string RoleType { get; set; }

        public string CreatedBy { get; set; }
    }
}
