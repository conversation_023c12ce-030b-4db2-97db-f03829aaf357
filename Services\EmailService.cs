﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using RestSharp;

namespace BIDConfirmation.Services
{
    public class EmailService
    {
        private readonly IConfiguration _config;

        public EmailService(IConfiguration config)
        {
            _config = config;
        }

        public async Task<bool> SendEmail(SendMailModel data)
        {
            string EmailApiBaseUrl = _config.GetSection("ExternalUrl").GetSection("EmailApiBaseUrl").Value;
            string SendEmail = _config.GetSection("ExternalUrl").GetSection("SendEmail").Value;
            JsonSerializerSettings jsonSerializerSettings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };
            RestClient restClient = new RestClient(EmailApiBaseUrl); // TODO
            restClient.RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;



            RestRequest request = new RestRequest(SendEmail, Method.POST);
            request.AddHeader("Accept", "application/json");
            string jsonObject = JsonConvert.SerializeObject(data, Formatting.Indented, jsonSerializerSettings);
            request.AddParameter("application/json", jsonObject, ParameterType.RequestBody);
            TaskCompletionSource<IRestResponse> taskCompletion = new TaskCompletionSource<IRestResponse>();
            RestRequestAsyncHandle handle = restClient.ExecuteAsync(request, r => taskCompletion.SetResult(r));
            RestResponse response = (RestResponse)(await taskCompletion.Task);
            //var response = await restClient.PostAsync(request);
            if (response.IsSuccessful)
            {
                //var result = JsonConvert.DeserializeObject<string>(response.Content, new ExpandoObjectConverter());
                //var r = Mapper.Map<string>(result);
                return true;
            }
            else
            {
                //Log.LogInformation("Email send failed");
                return false;
            }
        }
        public async Task<bool> SendEmailCc(SendMailCcModel data)
        {
            string EmailApiBaseUrl = _config.GetSection("ExternalUrl1").GetSection("EmailApiBaseUrl").Value;
            string SendEmail = _config.GetSection("ExternalUrl1").GetSection("SendEmail").Value;
            JsonSerializerSettings jsonSerializerSettings = new JsonSerializerSettings
            {
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };
            RestClient restClient = new RestClient(EmailApiBaseUrl); // TODO
            restClient.RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;



            RestRequest request = new RestRequest(SendEmail, Method.POST);
            request.AddHeader("Accept", "application/json");
            string jsonObject = JsonConvert.SerializeObject(data, Formatting.Indented, jsonSerializerSettings);
            request.AddParameter("application/json", jsonObject, ParameterType.RequestBody);
            //TaskCompletionSource<IRestResponse> taskCompletion = new TaskCompletionSource<IRestResponse>();
            //RestRequestAsyncHandle handle = restClient.ExecuteAsync(request, r => taskCompletion.SetResult(r));
            //RestResponse response = (RestResponse)(await taskCompletion.Task);'
            IRestResponse response = await restClient.ExecuteAsync(request);
            if (response.IsSuccessful)
            {
                //var result = JsonConvert.DeserializeObject<string>(response.Content, new ExpandoObjectConverter());
                //var r = Mapper.Map<string>(result);
                return true;
            }
            else
            {
                //Log.LogInformation("Email send failed");
                return false;
            }
        }
    }

    public class SendMailModel
    {
        public SendMailModel()
        {
        }
        //public List<string> To { get; set; }
        public string To { get; set; }
        //public string Cc { get; set; }
        public string From { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        // public string Name { get; set; }
    }
    public class SendMailCcModel
    {
        public SendMailCcModel()
        {
            To = new List<string>();
            Cc = new List<string>();
        }
        public List<string> To { get; set; }
        //public string To { get; set; }
        public List<string> Cc { get; set; }
        public string From { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        public string Name { get; set; }
    }
    public class SendMailAttcahModel
    {
        public SendMailAttcahModel()
        {
            To = new List<string>();
            Cc = new List<string>();
        }
        public List<string> To { get; set; }
        //public string To { get; set; }
        public List<string> Cc { get; set; }
        public string From { get; set; }
        public string Name { get; set; }
        public string Subject { get; set; }
        public string Body { get; set; }
        public List<string> FileAttachments { get; set; }
    }
}
