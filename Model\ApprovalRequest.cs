﻿namespace BIDConfirmation.Model
{
    public class ApprovalRequest
    {
        public bool Approved { get; set; }
        public string Reviewer { get; set; }

        public string TransactionReference { get; set; }
        public string Reason { get; set; } // Only required if rejected
    }

    public class TreasuryApprovalRequest
    {
        public bool Approved { get; set; }
        public string Reviewer { get; set; }
        public string Reason { get; set; }

        // Only for Treasury Review (Not saved to DB)
        public decimal? Amount { get; set; }
        public decimal? Rate { get; set; }
        public DateTime? ValueDate { get; set; }
    }

}
