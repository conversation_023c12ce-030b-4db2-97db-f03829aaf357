# Database Documentation

## Overview

The BID Confirmation Portal uses SQL Server as the primary database with Entity Framework Core 6.0 for data access. The database design follows a normalized structure with clear relationships between entities and comprehensive audit trails.

## Database Schema

### Entity Relationship Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   UserProfile   │    │ ApplicantDetails│    │    Document     │
│                 │    │                 │    │                 │
│ Id (PK)         │    │ Id (PK)         │    │ Id (PK)         │
│ FullName        │    │ FormMNumber     │    │ FileName        │
│ Email           │    │ ApplicantName   │    │ ContentType     │
│ RoleType        │    │ Status          │    │ FileData        │
│ IsActive        │    │ RMEmail         │    │ UploadedAt      │
│ CreatedOn       │    │ TradeReviewer   │    │ ApplicantDetailsId (FK)│
│ CreatedBy       │    │ TreasuryReviewer│    │                 │
└─────────────────┘    │ CreatedAt       │    └─────────────────┘
                       │ ValueDate       │              │
┌─────────────────┐    │ ...             │              │
│     Tokens      │    └─────────────────┘              │
│                 │              │                      │
│ Id (PK)         │              │ One-to-Many          │
│ Username        │              └──────────────────────┘
│ Token           │    
│ ExpirationTime  │    ┌─────────────────┐    ┌─────────────────┐
│ IsUsed          │    │ DocumentDownload│    │  RefreshToken   │
│ UsedAt          │    │                 │    │                 │
└─────────────────┘    │ Id (PK)         │    │ Id (PK)         │
                       │ DocumentId (FK) │    │ Token           │
┌─────────────────┐    │ UserId          │    │ Expires         │
│  UserActivity   │    │ DownloadedAt    │    │ Created         │
│                 │    └─────────────────┘    │ CreatedByIp     │
│ ActivityId (PK) │                           │ Revoked         │
│ UserName        │    ┌─────────────────┐    └─────────────────┘
│ ActionType      │    │   AuditTrail    │
│ RequestUrl      │    │                 │    ┌─────────────────┐
│ Timestamp       │    │ Id (PK)         │    │ FailedLoginAttempt│
│ IpAddress       │    │ UserId          │    │                 │
│ StatusCode      │    │ Action          │    │ Id (PK)         │
└─────────────────┘    │ Timestamp       │    │ Email           │
                       │ Details         │    │ IpAddress       │
┌─────────────────┐    └─────────────────┘    │ AttemptTime     │
│      Role       │                           └─────────────────┘
│                 │
│ Id (PK)         │
│ RoleType        │
│ Description     │
└─────────────────┘
```

## Core Entities

### 1. UserProfile
**Purpose**: Stores user account information and authentication data

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | string | PK, NOT NULL | Unique user identifier (GUID) |
| FullName | string | NOT NULL | User's full name |
| Email | string | NOT NULL, UNIQUE | User's email address |
| RoleType | string | NOT NULL | User role (Admin, RM, TRADE, TREASURY) |
| IsActive | bool | NOT NULL | Account status flag |
| CreatedOn | DateTime | NOT NULL | Account creation timestamp |
| DateLastUpdated | DateTime | NOT NULL | Last modification timestamp |
| CreatedBy | string | NULL | User who created the account |
| UpdatedBy | string | NULL | User who last updated the account |

**Indexes:**
- Primary Key: `Id`
- Unique Index: `Email`
- Index: `RoleType`

### 2. ApplicantDetails
**Purpose**: Stores Form M application data and workflow information

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | Guid | PK, NOT NULL | Unique application identifier |
| FormMNumber | string | NOT NULL | Form M reference number |
| ApplicantName | string | NOT NULL | Applicant company name |
| ApplicantAddress | string | NOT NULL | Applicant address |
| BeneficiaryName | string | NOT NULL | Beneficiary name |
| ApplicantTin | string | NOT NULL | Tax identification number |
| RCNumber | string | NOT NULL | Registration certificate number |
| ApplicantPhone | string | NOT NULL | Contact phone number |
| CountryOfSupply | string | NOT NULL | Country of goods supply |
| HSCode | string | NULL | Harmonized system code |
| Currency | string | NOT NULL | Transaction currency |
| TotalCFValue | decimal | NOT NULL | Total C&F value |
| FormMRegistrationDate | DateTime | NOT NULL | Form M registration date |
| GeneralGoodsDescription | string | NOT NULL | Description of goods |
| PaymentMode | string | NOT NULL | Payment method |
| TransactionReference | string | NULL | Transaction reference number |
| AccountNumber | string | NOT NULL | Customer account number |
| AccountName | string | NOT NULL | Account holder name |
| BVN | string | NOT NULL | Bank verification number |
| AvailableBalance | decimal | NOT NULL | Account available balance |
| PurchaseAmountUSD | decimal | NOT NULL | Purchase amount in USD |
| Rate | decimal | NOT NULL | Exchange rate |
| Tolerance | decimal | NULL | Rate tolerance percentage |
| PurchaseAmountInFormMCurrency | decimal | NOT NULL | Purchase amount in Form M currency |
| TreatedAmount | decimal | NULL | Treasury approved amount |
| Balance | decimal | NULL | Remaining balance |
| ValueDate | DateTime | NOT NULL | Transaction value date |
| CreatedAt | DateTime | NOT NULL | Application creation timestamp |
| Status | string | NOT NULL | Application status |
| TradeReviewer | string | NULL | Trade team reviewer email |
| RMEmail | string | NULL | Relationship manager email |
| TreasuryReviewer | string | NULL | Treasury team reviewer email |
| TradeReviewedAt | DateTime | NULL | Trade review timestamp |
| TreasuryReviewedAt | DateTime | NULL | Treasury review timestamp |
| RejectionReason | string | NULL | Rejection reason if applicable |
| IsDownloaded | bool | NOT NULL | Document download status |

**Indexes:**
- Primary Key: `Id`
- Index: `FormMNumber`
- Index: `Status`
- Index: `CreatedAt`
- Index: `RMEmail`

### 3. Document
**Purpose**: Stores uploaded documents and metadata

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | Guid | PK, NOT NULL | Unique document identifier |
| FileName | string | NOT NULL | Original file name |
| ContentType | string | NOT NULL | MIME type of the file |
| FileData | byte[] | NOT NULL | Binary file content |
| UploadedAt | DateTime | NOT NULL, DEFAULT GETUTCDATE() | Upload timestamp |
| ApplicantDetailsId | Guid | FK, NOT NULL | Reference to ApplicantDetails |

**Relationships:**
- Foreign Key: `ApplicantDetailsId` → `ApplicantDetails.Id` (CASCADE DELETE)

### 4. Tokens
**Purpose**: Stores authentication tokens for two-factor authentication

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | Guid | PK, NOT NULL | Unique token identifier |
| Username | string | NOT NULL | User email address |
| Token | string | NOT NULL | 6-digit authentication token |
| ExpirationTime | DateTime | NOT NULL | Token expiration time |
| IsUsed | bool | NOT NULL | Token usage status |
| UsedAt | DateTime | NULL | Token usage timestamp |

**Indexes:**
- Primary Key: `Id`
- Index: `Username`
- Index: `ExpirationTime`

### 5. RefreshToken
**Purpose**: Stores JWT refresh tokens for secure token renewal

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | Guid | PK, NOT NULL | Unique refresh token identifier |
| Token | string | NOT NULL | Base64 encoded refresh token |
| Expires | DateTime | NOT NULL | Token expiration time |
| Created | DateTime | NOT NULL | Token creation time |
| CreatedByIp | string | NOT NULL | IP address of token creation |
| Revoked | DateTime | NULL | Token revocation time |
| RevokedByIp | string | NULL | IP address of token revocation |
| ReplacedByToken | string | NULL | Replacement token reference |

## Audit and Logging Entities

### 6. UserActivity
**Purpose**: Comprehensive audit trail of user actions

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| ActivityId | string | PK, NOT NULL | Unique activity identifier |
| UserName | string | NOT NULL | User email address |
| ActionType | string | NOT NULL | HTTP method (GET, POST, etc.) |
| RequestUrl | string | NOT NULL | API endpoint accessed |
| Timestamp | DateTime | NOT NULL | Action timestamp |
| IpAddress | string | NOT NULL | User's IP address |
| StatusCode | int | NOT NULL | HTTP response status code |

### 7. AuditTrail
**Purpose**: System-level audit logging

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | Guid | PK, NOT NULL | Unique audit record identifier |
| UserId | string | NOT NULL | User identifier |
| Action | string | NOT NULL | Action performed |
| Timestamp | DateTime | NOT NULL | Action timestamp |
| Details | string | NULL | Additional details |

### 8. FailedLoginAttempt
**Purpose**: Security monitoring for failed authentication attempts

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | Guid | PK, NOT NULL | Unique attempt identifier |
| Email | string | NOT NULL | Attempted email address |
| IpAddress | string | NOT NULL | Source IP address |
| AttemptTime | DateTime | NOT NULL | Attempt timestamp |

### 9. DocumentDownload
**Purpose**: Tracks document download activities

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | Guid | PK, NOT NULL | Unique download record identifier |
| DocumentId | Guid | FK, NOT NULL | Reference to Document |
| UserId | Guid | NOT NULL | User who downloaded |
| DownloadedAt | DateTime | NOT NULL, DEFAULT GETUTCDATE() | Download timestamp |

**Relationships:**
- Foreign Key: `DocumentId` → `Document.Id` (CASCADE DELETE)

### 10. Role
**Purpose**: Defines available user roles

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| Id | Guid | PK, NOT NULL | Unique role identifier |
| RoleType | string | NOT NULL, UNIQUE | Role name |
| Description | string | NULL | Role description |

## Entity Framework Configuration

### DbContext Configuration

```csharp
public class ApplicationDbContext : DbContext
{
    // DbSets
    public DbSet<UserProfile> Users { get; set; }
    public DbSet<Tokens> Tokens { get; set; }
    public DbSet<UserActivity> UserActivities { get; set; }
    public DbSet<FailedLoginAttempt> FailedLoginAttempts { get; set; }
    public DbSet<AuditTrail> AuditTrails { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<Document> Documents { get; set; }
    public DbSet<ApplicantDetails> ApplicantDetails { get; set; }
    public DbSet<DocumentDownload> DocumentDownloads { get; set; }
    public DbSet<RefreshToken> RefreshTokens { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        // Configure relationships
        builder.Entity<ApplicantDetails>()
               .HasMany(a => a.Documents)
               .WithOne(d => d.Applicant)
               .HasForeignKey(d => d.ApplicantDetailsId)
               .OnDelete(DeleteBehavior.Cascade);

        // Configure default values
        builder.Entity<Document>()
               .Property(d => d.UploadedAt)
               .HasDefaultValueSql("GETUTCDATE()");
    }
}
```

## Migration History

### Migration Timeline

| Migration | Date | Description |
|-----------|------|-------------|
| UpdatedUserProfile | 2025-07-22 | Initial user profile structure |
| RefactoredUser | 2025-07-28 | User model refactoring |
| RefactoredRejection | 2025-07-28 | Rejection reason handling |
| addeddownloadedendpoint | 2025-07-30 | Document download tracking |
| AddedIsdownloaded | 2025-07-31 | Download status flag |
| AddedTransactRef | 2025-08-05 | Transaction reference field |
| MadeUpdatedbyNullable | 2025-08-18 | Nullable UpdatedBy field |
| AddedTreatedAmount | 2025-08-21 | Treasury treated amount |
| madeToleranceNullable | 2025-08-21 | Nullable tolerance field |
| AddedTokenUsage | 2025-09-08 | Token usage tracking |
| RefreshTokenAdded | 2025-09-09 | Refresh token implementation |
| RefreshToken | 2025-09-09 | Refresh token refinement |

## Data Access Patterns

### Repository Pattern
- **UserAccountRepository**: External data access (Oracle)
- **Entity Framework**: Primary database operations
- **Dapper**: Performance-critical queries

### Connection Management
- **Primary Database**: SQL Server via Entity Framework
- **External Database**: Oracle via Dapper
- **Connection Pooling**: Automatic via .NET

## Database Maintenance

### Backup Strategy
- **Full Backup**: Daily
- **Transaction Log Backup**: Every 15 minutes
- **Retention**: 30 days

### Performance Optimization
- **Indexes**: Strategic indexing on frequently queried columns
- **Query Optimization**: Use of Dapper for complex queries
- **Connection Pooling**: Efficient connection management

### Security Measures
- **Encrypted Connections**: TrustServerCertificate=True
- **Parameterized Queries**: SQL injection prevention
- **Access Control**: Database-level permissions
- **Audit Logging**: Complete activity tracking

This database documentation provides comprehensive coverage of the data model and access patterns for the BID Confirmation Portal system.
