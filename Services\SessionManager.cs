﻿using Newtonsoft.Json;

namespace BidConfirmationAPI.Services
{
    public class SessionManager
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private const string SessionKey = "UserSession";

        public SessionManager(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public void InitializeSession(string userEmail)
        {
            var session = new UserSession
            {
                UserEmail = userEmail,
                LastActivityTime = DateTime.UtcNow
            };

            SetSession(session);
        }

        public UserSession GetSession()
        {
            var sessionJson = _httpContextAccessor.HttpContext.Session.GetString(SessionKey);
            return sessionJson != null ? JsonConvert.DeserializeObject<UserSession>(sessionJson) : null;
        }

        public void RenewSession()
        {
            var session = GetSession();
            if (session != null)
            {
                session.LastActivityTime = DateTime.UtcNow;
                SetSession(session);
            }
        }

        public void EndSession()
        {
            _httpContextAccessor.HttpContext.Session.Remove(SessionKey);
        }

        public bool IsSessionActive()
        {
            var session = GetSession();
            if (session != null)
            {
                // Check session expiry (e.g., 30 minutes of inactivity)
                var sessionTimeout = TimeSpan.FromMinutes(30);
                return (DateTime.UtcNow - session.LastActivityTime) < sessionTimeout;
            }
            return false;
        }
        private void SetSession(UserSession session)
        {
            var sessionJson = JsonConvert.SerializeObject(session);
            _httpContextAccessor.HttpContext.Session.SetString(SessionKey, sessionJson);
        }
    }
    public class UserSession
    {
        public string UserEmail { get; set; }
        public DateTime LastActivityTime { get; set; }
    }
}
