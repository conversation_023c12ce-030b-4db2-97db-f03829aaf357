{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",

  "ConnectionStrings": {
    "BID_CONFIRMATIONDB": "server=************;Database=BID_CONFIRMATION;uid=Automationadmin;password=Automationuser@123;TrustServerCertificate=True",
    "NIBSSDB": "server=************;Database=NIBBS_INWARD;uid=nibbsuser;password=********;",
    "AccountQuery": "select CUSTOM.effbal(acid,'01') as avail_bal,custom.getbvn(foracid) as bvn,lien_amt,acct_name from tbaadm.gam where foracid = :AccountNo",
    //"*******Query": "SELECT APPLICANT_NAME, FORM_NUMBER, APPLICATION_NUMBER, TOT_FOB_VAL_AMT, GOODS_DESCRIPTION, PAYMENT_MODE, FUNDS_SOURCE, ACCOUNT_NUMBER, EXCHANGE_RATE, TOT_CNF_VAL_AMT, SU<PERSON>ISSION_DATE, APPLICANT_RCNUMBER, APPLICANT_PHONE, COUNTRY_OF_SUPPLY, TOT_CNF_VAL_CURR, CREATED_AT, APPLICANT_ADDRESS, BENEFICIARY_NAME, APPLICANT_TIN FROM KACHASI_UAT.FORM_M WHERE FORM_NUMBER = :FORM_NUMBER",
    "*******Query": "SELECT f.applicant_name, f.form_number, f.application_number, f.tot_fob_val_amt, f.goods_description, f.payment_mode, p.item_decription AS Payment_Description, f.funds_source, f.account_number, f.exchange_rate, f.tot_cnf_val_amt, f.submission_date, f.applicant_rcnumber, f.applicant_phone, f.country_of_supply, f.tot_cnf_val_curr, f.created_at, f.applicant_address, f.beneficiary_name, f.applicant_tin FROM kachasi_uat.pick_list_item p JOIN kachasi_uat.form_m f ON p.item_code = f.PAYMENT_MODE WHERE PICKLIST_ID ='32'  AND form_number = :FORM_NUMBER",
    //"*******": null,
    //"OraConn": "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=FINWEMA))); User Id=selector;Password=selector;",
    "OracleConnection": "Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=WEMA-HQ-UAT-SCAN)(PORT=1530))(CONNECT_DATA=(SERVICE_NAME=WEMAUAT))); User Id=selector;Password=selector;",
    "*******Connection": "Data Source=(DESCRIPTION=(ADDRESS_LIST=(ADDRESS=(PROTOCOL=TCP)(HOST=wema-hq-uat-scan)(PORT=1530)))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=TRADEDB)));User Id=KACHASIUSER;Password=*******$Form13; Connection Timeout=120;"

    // "OraConn": "User Id=selector;Password=selector;Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=************)(PORT=1521))(CONNECT_DATA=(SERVICE_NAME=finwema)));Pooling=True;Max Pool Size=10000;Connection Timeout=10000;"
  },
  "JwtSettings": {
    "SecretKey": "a4c6283e748bb2539e1414c1d8d2b9208e00762231cb4eac8a93597bf1c65a1f",
    "Issuer": "your-app-name",
    "Audience": "your-app-users",
    "ExpiryMinutes": 30
  },
  "ExternalUrl": {
    //"WebHookQuery": "@ INSERT INTO Transactions (ConsentId, CreditType, Amount, Currency, TransactionReference, TransactionDate, TransactionInformation) VALUES (@ConsentId, @CreditType, @Amount, @Currency, @TransactionReference, @TransactionDate, @TransactionInformation)";"
    "BaseURL": "https://preprod.connect.e-doconline.ng/api/v1/external/consent",
    "iWemaBaseUrl": "http://************",
    "iWemaLoginPath": "/api/v1/Users/<USER>",

    "EmailApiBaseUrl": "http://************/EmailApi",
    "SendEmail": "/api/SendEmail",
    "LDapServerIP": "***********",
    "LDapServerPort": "389",

    "ProxyUrl": "**********",
    "ProxyPort": "8080",
    "emailProperties": "<EMAIL>",
    "RM": "<EMAIL>",
    "Treasury": "<EMAIL>",
    "Trade": "<EMAIL>"

  },
  "ExternalUrl1": {
    "EmailApiBaseUrl": "http://***********/EmailServiceWithCC",
    "SendEmail": "/api/Email/SendMail",
    //"CcMails": "<EMAIL>",
    "SenderMail": "<EMAIL>"
    //"SendEmail": "/api/Email/SendMail"
  },
  //"ApiSettings": {
  //  "WebhookSecret": "your-webhook-secret-key",
  //  "ExpectedSignatureHeader": "x-signature",
  //  "BearerToken": "594773afb62265939eec6ca957f47764",
  //  "ClientId": "dGVzdF81ODk="
  //},
  "ApiSettings": {
    "WebhookSecret": "your-webhook-secret-key",
    "ExpectedSignatureHeader": "x-signature",
    "BearerToken": "fc7a41e14f6f0d8714ed378676bf6f37",
    "ClientId": "d2VtYWJhbmt0ZXN0dW5pdHNfYWZmb3JkYWJpbGl0eV9mdWxsXzY1NQ=="
  },
  "ExternalPath": {
    "iwemaLoginPath": "http://iwema/api/v1/users/login"
  },
  "Settings": {
    "Sender": "<EMAIL>"
  }
}
