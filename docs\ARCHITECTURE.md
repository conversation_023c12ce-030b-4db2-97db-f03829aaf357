# System Architecture

## Overview

The BID Confirmation Portal API follows a layered architecture pattern with clear separation of concerns. The system is built using .NET 6 Web API with Entity Framework Core for data access and implements a role-based security model.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Applications                    │
│                  (Web/Mobile Clients)                      │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTPS/REST API
┌─────────────────────▼───────────────────────────────────────┐
│                  API Gateway Layer                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐   │
│  │   CORS      │ │ JWT Auth    │ │  Cutoff Time        │   │
│  │ Middleware  │ │ Middleware  │ │   Middleware        │   │
│  └─────────────┘ └─────────────┘ └─────────────────────┘   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Controller Layer                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐   │
│  │   Admin     │ │ UserAccount │ │    Weather          │   │
│  │ Controller  │ │ Controller  │ │   Controller        │   │
│  └─────────────┘ └─────────────┘ └─────────────────────┘   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Service Layer                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐   │
│  │Authentication│ │UserAccount  │ │     Email           │   │
│  │   Service   │ │  Service    │ │    Service          │   │
│  └─────────────┘ └─────────────┘ └─────────────────────┘   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│               Repository Layer                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐   │
│  │ UserAccount │ │   Entity    │ │     Dapper          │   │
│  │ Repository  │ │ Framework   │ │   Queries           │   │
│  └─────────────┘ └─────────────┘ └─────────────────────┘   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Data Layer                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐   │
│  │ SQL Server  │ │   Oracle    │ │      LDAP           │   │
│  │  Database   │ │  Database   │ │   Directory         │   │
│  └─────────────┘ └─────────────┘ └─────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Technology Stack

### Core Framework
- **.NET 6**: Latest LTS version of .NET
- **ASP.NET Core Web API**: RESTful API framework
- **C# 10**: Programming language with nullable reference types enabled

### Data Access
- **Entity Framework Core 6.0**: Primary ORM for database operations
- **Dapper 2.1.66**: Micro-ORM for complex queries and performance-critical operations
- **SQL Server**: Primary database for application data
- **Oracle Database**: External database for account information queries

### Authentication & Security
- **JWT Bearer Authentication**: Token-based authentication
- **LDAP Integration**: Staff authentication via Active Directory
- **BCrypt.Net**: Password hashing (for non-LDAP users)
- **Role-based Authorization**: Custom authorization handlers

### Logging & Monitoring
- **NLog**: Structured logging framework
- **File-based Logging**: Logs stored in `C:\Logs\BIDCONFIRMATION\`

### Documentation & Testing
- **Swagger/OpenAPI**: API documentation and testing interface
- **Swashbuckle.AspNetCore**: Swagger implementation for .NET

### Additional Libraries
- **Mapster**: Object-to-object mapping
- **Newtonsoft.Json**: JSON serialization
- **ClosedXML**: Excel file generation
- **RestSharp**: HTTP client for external API calls
- **Polly**: Resilience and transient-fault-handling

## Project Structure

### Controllers
- **AdminController**: User management and administrative functions
- **UserAccountController**: Core business operations (bid submission, approvals)
- **WeatherForecastController**: Sample controller (can be removed)

### Services
- **AuthenticationService**: Handles login, token generation, and user management
- **UserAccountService**: Business logic for account operations
- **EmailService**: Email notification functionality
- **SessionManager**: Session and token management

### Repositories
- **UserAccountRepository**: Data access for external account information
- **ApplicationDbContext**: Entity Framework database context

### Models
- **Entity Models**: Database entities (UserProfile, ApplicantDetails, Document, etc.)
- **DTOs**: Data transfer objects for API communication
- **Request/Response Models**: API contract definitions

### Middleware
- **CutoffTimeMiddleware**: Enforces business hour restrictions for certain roles

## Database Design

### Primary Database (SQL Server)
- **Users**: User profiles and authentication data
- **ApplicantDetails**: Form M application data
- **Documents**: File storage and metadata
- **Tokens**: Authentication tokens and refresh tokens
- **UserActivities**: Audit trail for user actions
- **AuditTrails**: System audit logs
- **Roles**: User role definitions

### External Databases
- **Oracle Database**: Account information queries
- **NIBSS Database**: External banking data

## Security Architecture

### Authentication Flow
1. User provides credentials (email/password)
2. System validates against LDAP directory
3. JWT token generated with user claims
4. Refresh token created for token renewal
5. Subsequent requests validated using JWT

### Authorization Model
- **Role-based Access Control (RBAC)**
- **Custom Authorization Handlers**
- **Policy-based Authorization**

### Security Features
- **Rate Limiting**: Failed login attempt protection
- **Token Expiration**: Short-lived JWT tokens (5 minutes)
- **Refresh Token Rotation**: Secure token renewal
- **Audit Logging**: Complete activity tracking
- **CORS Configuration**: Cross-origin request handling

## Data Flow

### Bid Submission Flow
1. RM submits Form M with documents
2. Data validated and stored in database
3. Documents stored as binary data
4. Trade team notified via email
5. Status updated to "Submitted"

### Approval Workflow
1. Trade team reviews application
2. Approval/rejection recorded with reasons
3. Treasury team notified if approved
4. Final processing by treasury
5. Status updates and email notifications

## Integration Points

### External Systems
- **LDAP Directory**: Staff authentication
- **Email Service**: Notification system
- **Oracle Database**: Account information
- **External APIs**: Form M validation services

### Configuration Management
- **appsettings.json**: Application configuration
- **Environment Variables**: Sensitive configuration
- **Connection Strings**: Database connections
- **JWT Settings**: Token configuration

## Scalability Considerations

### Performance Optimizations
- **Dapper for Complex Queries**: Performance-critical operations
- **Entity Framework for CRUD**: Standard database operations
- **Async/Await Pattern**: Non-blocking operations
- **Connection Pooling**: Database connection management

### Monitoring & Logging
- **Structured Logging**: JSON-formatted logs
- **Error Tracking**: Comprehensive error logging
- **Performance Metrics**: Request/response timing
- **Audit Trail**: Complete user activity tracking

## Deployment Architecture

### Development Environment
- **Local SQL Server**: Development database
- **Local LDAP**: Development authentication
- **File-based Logging**: Local log files

### Production Environment
- **SQL Server Cluster**: High availability database
- **Enterprise LDAP**: Production authentication
- **Centralized Logging**: Log aggregation system
- **Load Balancer**: Multiple API instances

This architecture provides a robust, secure, and scalable foundation for the BID Confirmation Portal API system.
