﻿using BidConfirmationAPI.Model;
using System.ComponentModel.DataAnnotations.Schema;

namespace BIDConfirmation.Model
{
    public class Document
    {
        public Guid Id { get; set; } = Guid.NewGuid();

        public string FileName { get; set; }

        public string ContentType { get; set; }

        public byte[] FileData { get; set; }

        public DateTime UploadedAt { get; set; }

        public Guid ApplicantDetailsId { get; set; }

        [ForeignKey("ApplicantDetailsId")]
        public ApplicantDetails Applicant { get; set; }
    }

    public class DocumentDownload
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public Guid DocumentId { get; set; }
        public Guid UserId { get; set; }  // ✅ User ID instead of email
        public DateTime DownloadedAt { get; set; } = DateTime.UtcNow;

        public Document Document { get; set; }
    }



}
