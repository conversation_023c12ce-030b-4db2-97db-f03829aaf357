<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="Dapper" Version="2.1.66" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.36" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.1.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.36" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.36" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.36">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="6.0.1" />
    <PackageReference Include="Oracle.ManagedDataAccess.Core" Version="3.21.190" />
    <PackageReference Include="Polly" Version="8.6.2" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageReference Include="RestSharp" Version="106.12.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>

</Project>
