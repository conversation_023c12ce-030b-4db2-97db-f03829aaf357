﻿using System.ComponentModel.DataAnnotations;

namespace BidConfirmationAPI.Models
{
    public class UserActivity
    {
        [Key]
        public string ActivityId { get; set; }
        public string? UserName { get; set; }
        public DateTime Timestamp { get; set; }
        public string IpAddress { get; set; }
        public int StatusCode { get; set; }
        public string? ActionType { get; set; }
        public string? RequestUrl { get; set; }
    }
}
