﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
	  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	  autoReload="true"
	  internalLogLevel="Trace"
	  internalLogFile="C:\Logs\BIDCONFIRMATION\internallog.txt">
	<targets>
		<target name="logfile" xsi:type="File"
				fileName="C:\Logs\BIDCONFIRMATION\${shortdate}_logfile.txt"
				layout="${longdate} ${level:uppercase=true} ${message}"/>
	</targets>
	<rules>
		<logger name="*" minlevel="Debug" writeTo="logfile" />
	</rules>
</nlog>