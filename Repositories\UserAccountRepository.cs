using BIDConfirmation.Model;
using BidConfirmationAPI.Interfaces;
using BidConfirmationAPI.Model;
using Dapper;
using Oracle.ManagedDataAccess.Client;
using System.Data;

namespace BidConfirmationAPI.Repositories
{
    public class UserAccountRepository : IUserAccountRepository
    {
        private readonly string _connectionString;

        public UserAccountRepository(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("OracleConnection") 
                ?? throw new ArgumentNullException(nameof(configuration));
        }

        public async Task<UserAccountDetails?> GetDetailsByAccountNoAsync(string accountNo)
        {
            var query = Util.AppConfiguration().GetSection("ConnectionStrings").GetSection("AccountQuery").Value;
            //const string query = @"
            //    SELECT 
            //        CUSTOM.effbal(acid,'01') as Avail_Bal,
            //        custom.getbvn(foracid) as Bvn,
            //        lien_amt as Lien_Amt 
            //    FROM tbaadm.gam 
            //    WHERE foracid = :AccountNo";

            using var connection = new OracleConnection(_connectionString);
            
            var result = await connection.QueryFirstOrDefaultAsync<UserAccountDetails>(
                query, 
                new { AccountNo = accountNo });

            return result;
        }

        public async Task<KachasiDetails?> GetKachasiDetailsAsync(string FormNumber)
        {
            var connectionString = Util.AppConfiguration().GetSection("ConnectionStrings").GetSection("KachasiConnection").Value;
            var query = Util.AppConfiguration().GetSection("ConnectionStrings").GetSection("KachasiQuery").Value;

            using var connection = new OracleConnection(connectionString);

            var result = await connection.QueryFirstOrDefaultAsync<KachasiDetails>(
                query,
                new { FORM_NUMBER = FormNumber });

            return result;
        }

        //public async Task<KachasiDetails?> GetKachasiDetailAsync(string PickupListID, string FormNumber)
        //{
        //    var connectionString = Util.AppConfiguration().GetSection("ConnectionStrings").GetSection("KachasiConnection").Value;
        //    var query = Util.AppConfiguration().GetSection("ConnectionStrings").GetSection("Kachasi").Value;

        //    using var connection = new OracleConnection(connectionString);

        //    var result = await connection.QueryFirstOrDefaultAsync<KachasiDetails>(
        //        query,
        //        new { PickupListID = PickupListID,  FORM_NUMBER = FormNumber });

        //    return result;
        //}
    }
}