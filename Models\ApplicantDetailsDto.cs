﻿namespace BIDConfirmation.Models
{
    public class ApplicantDetailsDto
    {
        public Guid Id { get; set; }

        public string FormMNumber { get; set; }
        public string ApplicantName { get; set; }
        public string ApplicantAddress { get; set; }
        public string BeneficiaryName { get; set; }
        public string ApplicantTin { get; set; }
        public string RCNumber { get; set; }
        public string ApplicantPhone { get; set; }
        public string CountryOfSupply { get; set; }
        public string? HSCode { get; set; }
        public string Currency { get; set; }
        public decimal TotalCFValue { get; set; }
        public DateTime FormMRegistrationDate { get; set; }
        public string GeneralGoodsDescription { get; set; }

        public string? TransactionReference { get; set; }
        public string PaymentMode { get; set; }

        // From accountInfo
        public string AccountNumber { get; set; }
        public string AccountName { get; set; }
        public string BVN { get; set; }
        public decimal AvailableBalance { get; set; }

        // From rateInfo
        public decimal PurchaseAmountUSD { get; set; }
        public decimal Rate { get; set; }
        public decimal Tolerance { get; set; }
        public decimal PurchaseAmountInFormMCurrency { get; set; }

        public decimal? TreatedAmount { get; set; }
        public decimal? Balance { get; set; }

        public DateTime ValueDate { get; set; }

        public DateTime CreatedAt { get; set; }

        public string Status { get; set; }
        public string? TradeReviewer { get; set; }
        public string? TreasuryReviewer { get; set; }
        public DateTime? TradeReviewedAt { get; set; }
        public DateTime? TreasuryReviewedAt { get; set; }
        public string? RejectionReason { get; set; }
        public bool IsDownloaded { get; set; }
        public List<DocumentDto> Documents { get; set; }
    }

    public class DocumentDto
    {
        public Guid Id { get; set; }
        public string FileName { get; set; }
        public string ContentType { get; set; }
        public DateTime UploadedAt { get; set; }
    }
}
