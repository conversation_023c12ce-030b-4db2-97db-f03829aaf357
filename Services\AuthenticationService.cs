﻿using Azure.Core;
using BidConfirmationAPI.Data;
using BidConfirmationAPI.Interfaces;
using BidConfirmationAPI.Model;
using BidConfirmationAPI.Models;
using Mapster;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using RestSharp;
using System.DirectoryServices.Protocols;
using System.IdentityModel.Tokens.Jwt;
using System.Net;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;


namespace BidConfirmationAPI.Services
{
    public class AuthenticationService : IAuthenticationService
    {
        private readonly IConfiguration _configuration;
        private readonly ApplicationDbContext _dbContext;
        private readonly SessionManager _sessionManager;
        private readonly IHttpContextAccessor _httpContextAccessor;
        public AuthenticationService(IConfiguration configuration, ApplicationDbContext dbContext, SessionManager sessionManager, IHttpContextAccessor httpContextAccessor)
        {
            _configuration = configuration;
            _dbContext = dbContext;
            _sessionManager = sessionManager;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<Result> CreateUser(UserProfileCreate entity)
        {
            try
            {
                var Sender = Util.AppConfiguration().GetSection("Settings").GetSection("Sender").Value;
                string subject = "Password Generated";
                //string body = $"Hello!,\n\nThis is your paswword: {token}";

                var newEntity = entity.Adapt<UserProfile>();
                var userExist = _dbContext.Users.Where(x => x.Email == newEntity.Email).FirstOrDefault();

                if (string.IsNullOrEmpty(newEntity.Email))
                {
                    return new ErrorResult("Invalid input details!");
                }
                if (userExist != null)
                {
                    return new ErrorResult("User already exist");
                }
                newEntity.Id = Guid.NewGuid().ToString();
                newEntity.IsActive = true;
                newEntity.UpdatedBy = "System";
                //newEntity.PasswordChanged = false;

                if (!newEntity.Email.EndsWith("@wemabank.com"))
                {
                    if (!IsValidEmail(newEntity.Email))
                    {
                        return new ErrorResult("Invalid email format!");
                    }
                    //var passWord = newEntity.GenerateAutoPassword();
                    string[] nameParts = entity.FullName.Split(' ');
                    string firstName = nameParts[0];

                    //var mail = new SendMailModel()
                    //{
                    //    Subject = subject,
                    //    Body = $"Hello {firstName}!,\n\nThis is your password: {passWord}",
                    //    To = newEntity.Email,
                    //    From = Sender
                    //};
                    //await SendInstanceEmail(mail);


                    //var hashedPassword = HashPassword(passWord);
                    //newEntity.Password = hashedPassword;


                }
                await _dbContext.Users.AddAsync(newEntity);
                await _dbContext.SaveChangesAsync();

                return new SuccessResult("Created user successfully!");
            }
            catch (Exception ex)
            {

                //throw;
                AppLogger.LogInformation($"{ex.Message}");
                return new ErrorResult($"{ex.Message}");
            }

        }

        //public async Task<Result> CreateUser(UserProfileCreate entity)
        //{
        //    try
        //    {
        //        if (string.IsNullOrWhiteSpace(entity.Email))
        //            return new ErrorResult("Email is required!");

        //        // Check valid email format
        //        if (!IsValidEmail(entity.Email))
        //            return new ErrorResult("Invalid email format!");

        //        // Ensure only @wemabank.com emails are allowed
        //        if (!entity.Email.EndsWith("@wemabank.com", StringComparison.OrdinalIgnoreCase))
        //            return new ErrorResult("Only Wemabank.c email addresses are allowed for internal users!");

        //        // Check if user already exists
        //        var userExist = await _dbContext.Users
        //            .FirstOrDefaultAsync(x => x.Email.ToLower() == entity.Email.ToLower());

        //        if (userExist != null)
        //            return new ErrorResult("User already exists!");

        //        // Map to entity
        //        var newEntity = entity.Adapt<UserProfile>();
        //        newEntity.Id = Guid.NewGuid().ToString();
        //        newEntity.IsActive = true;
        //        newEntity.UpdatedBy = "System";

        //        // Generate default password if needed
        //        // var passWord = newEntity.GenerateAutoPassword();
        //        // newEntity.Password = HashPassword(passWord);

        //        await _dbContext.Users.AddAsync(newEntity);
        //        await _dbContext.SaveChangesAsync();

        //        // Optionally send welcome email
        //        // string firstName = entity.FullName?.Split(' ').FirstOrDefault() ?? "User";
        //        // var mail = new SendMailModel
        //        // {
        //        //     Subject = "Password Generated",
        //        //     Body = $"Hello {firstName},<br><br>This is your password: {passWord}",
        //        //     To = newEntity.Email,
        //        //     From = Util.AppConfiguration().GetSection("Settings").GetSection("Sender").Value
        //        // };
        //        // await SendInstanceEmail(mail);

        //        return new SuccessResult("Created user successfully!");
        //    }
        //    catch (Exception ex)
        //    {
        //        AppLogger.LogInformation($"{ex.Message}");
        //        return new ErrorResult("An error occurred while creating the user.");
        //    }
        //}


        private bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                return false;
            }

            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
       

        public string AuthenticateStaff(string StaffEmail, string StaffPassword)
        {
            if (!string.IsNullOrEmpty(StaffEmail.ToLower()) && !StaffEmail.ToLower().Contains("@wemabank.com"))
            {
                StaffEmail = StaffEmail + "@wemabank.com";
            }
            bool result = false;

            string LDapServerIP = _configuration.GetSection("ExternalUrl").GetSection("LDapServerIP").Value;// _config.AppSettings["LDapServerIP"];
            int LDapServerPort = Convert.ToInt32(_configuration.GetSection("ExternalUrl").GetSection("LDapServerPort").Value); //Convert.ToInt32(_config.AppSettings["LDapServerPort"]);

            try
            {
                LdapDirectoryIdentifier ldi = new LdapDirectoryIdentifier(LDapServerIP, LDapServerPort);
                LdapConnection ldapConnection = new LdapConnection(ldi);
                // LDap successfully created at this point

                ldapConnection.AuthType = AuthType.Basic;
                ldapConnection.SessionOptions.ProtocolVersion = 3;
                NetworkCredential nc = new NetworkCredential(StaffEmail, StaffPassword);

                // LDap Binds and Authenticates at this point
                ldapConnection.Bind(nc);

                return "true";
            }
            catch (LdapException e)
            {
                AppLogger.LogError($"{StaffEmail} login failed with error: {e.Message}");
                return e.Message;
            }
            catch (Exception e)
            {
                AppLogger.LogError($"{StaffEmail} login failed with error: {e.Message}");
                return "Internal Server Error. Contact Support";
            }

            //return result;
        }
        public async Task<Result> LoginAsync(LoginCreate entity, string ipAddress)
        {
            try
            {
                var Sender = Util.AppConfiguration().GetSection("Settings").GetSection("Sender").Value;
                string subject = "Authentication Token";

                if (!string.IsNullOrEmpty(entity.Email.ToLower()) && !entity.Email.ToLower().Contains("@wemabank.com"))
                {
                    entity.Email = entity.Email + "@wemabank.com";
                }

                var userProfile = await _dbContext.Users.FirstOrDefaultAsync(x => x.Email == entity.Email);
                var newUserProfile = userProfile.Adapt<UserProfileModel>();
                if (userProfile == null)
                {
                    IncrementFailedLoginAttempts(entity.Email, ipAddress);
                    bool rateLimitExceeded = RateLimitExceeded(entity.Email, ipAddress);
                    if (rateLimitExceeded)
                    {
                        return new ErrorResult { Status = 429, Message = "Account temporarily locked. Please try again after one hour." };
                    }
                    AppLogger.LogError($"Unauthorized: {entity.Email} not found");
                    return new ErrorResult { Status = 401, Message = "User not found" };
                }

                if (userProfile.IsActive == false)
                {
                    IncrementFailedLoginAttempts(entity.Email, ipAddress);
                    bool rateLimitExceeded = RateLimitExceeded(entity.Email, ipAddress);
                    if (rateLimitExceeded)
                    {
                        return new ErrorResult { Status = 429, Message = "Account temporarily locked. Please try again after one hour." };
                    }
                    AppLogger.LogError($"Unauthorized: {entity.Email} deactivated");
                    return new ErrorResult { Status = 401, Message = "User deactivated" };
                }

                // Enforce 3:00 PM cut-off time for RMs and Trade users
                var now = DateTime.Now.TimeOfDay;

                AppLogger.LogInformation($"Current Time: {now}");
                var cutOff = new TimeSpan(15, 0, 0); // 3:00 PM

                if ((userProfile.RoleType == "RM" || userProfile.RoleType == "TRADE") && now >= cutOff)
                {
                    AppLogger.LogError($"Access denied: {userProfile.RoleType} {userProfile.Email} tried to log in after 3:00 PM");
                    return new ErrorResult
                    {
                        Status = 403,
                        Message = "Bid submission is closed for today. Please try again before 3:00 PM."
                    };
                }

                var loginResult = AuthenticateStaff(entity.Email, entity.Password);
                if (loginResult != "true")
                {
                    IncrementFailedLoginAttempts(entity.Email, ipAddress);
                    bool rateLimitExceeded = RateLimitExceeded(entity.Email, ipAddress);
                    if (rateLimitExceeded)
                    {
                        return new ErrorResult { Status = 429, Message = "Account temporarily locked. Please try again after one hour." };
                    }
                    return new ErrorResult(loginResult);
                }


                //var token = generateJwtToken(newUserProfile);

                var auditLog = new UserActivity
                {
                    ActivityId = Guid.NewGuid().ToString(),
                    UserName = entity.Email,
                    ActionType = "POST",
                    RequestUrl = "api/Login",
                    Timestamp = DateTime.UtcNow.AddHours(1),
                    IpAddress = ipAddress,
                    StatusCode = 200,
                };

                _dbContext.UserActivities.Add(auditLog);
                await _dbContext.SaveChangesAsync();

                //Check if a token has been generated for the user

                var tokenData = _dbContext.Tokens.FirstOrDefault(t => t.Username == entity.Email);


                if (tokenData == null)
                {
                    string tokenGotten = GenerateToken(entity.Email);
                    string[] nameParts = userProfile.FullName.Split(' ');
                    string firstName = nameParts[0];
                    string body = $"Hello {firstName}!,\n\nYour authentication token is: {tokenGotten}";

                    var mail = new SendMailModel()
                    {
                        Subject = subject,
                        Body = body,
                        To = userProfile.Email,
                        From = Sender
                    };
                    await SendInstanceEmail(mail);
                    //await SendTokenByEmail(entity.Email, tokenGotten);
                    var message = "Token sent to email for user";
                    // Return a message indicating token sent

                    AppLogger.LogInformation($"{message}");
                    return new SuccessResult { Message = message };
                }



                // Check if the difference between DateTime.Now and the expiration time is greater than or equal to 10 minutes
                TimeSpan timeDifference = tokenData.ExpirationTime - DateTime.Now;
                if (timeDifference.TotalMinutes >= 1)
                {
                    if (ValidateToken(userProfile.Email, tokenData.Token))
                    {
                        AppLogger.LogError("Token is still valid");

                        var message = "Token is still valid";

                        return new Result { Message = message };
                    }
                    else
                    {
                        AppLogger.LogError("Token validation failed");
                        tokenData.ExpirationTime = DateTime.Now.AddMinutes(-1); // Invalidating the token
                        _dbContext.SaveChanges(); // Save changes to the database
                        return new ErrorResult { Message = "Invalid token. Please log in again." };
                    }
                }
                else
                {
                    AppLogger.LogError("Token has expired or is about to expire");

                    // Generate new token
                    string newToken = GenerateToken(entity.Email);
                    AppLogger.LogInformation("New token generated");

                    // Update token information
                    tokenData.Token = newToken;
                    tokenData.ExpirationTime = DateTime.Now.AddMinutes(5);

                    // Save changes to the database
                    await _dbContext.SaveChangesAsync();

                    // Send new token by email
                    string[] nameParts = userProfile.FullName.Split(' ');
                    string firstName = nameParts[0];
                    string body = $"Hello {firstName}!,\n\nYour authentication token is: {newToken}";

                    var mail = new SendMailModel()
                    {
                        Subject = subject,
                        Body = body,
                        To = userProfile.Email,
                        From = Sender
                    };
                    await SendInstanceEmail(mail);
                    //await SendTokenByEmail(entity.Email, newToken);

                    // Log information
                    var message = "Verification token sent to email";
                    //if (!_sessionManager.IsSessionActive())
                    //{
                    //    // End the previous session if it's not active
                    //    _sessionManager.EndSession();
                    //}
                    //_sessionManager.InitializeSession(entity.Email);

                    return new SuccessResult { Message = message };
                    AppLogger.LogInformation(message);


                }
            }
            catch (Exception ex)
            {
                AppLogger.LogError($"Login error: {ex.Message}");
                return new ErrorResult { Message = "Login failed. Try again later." };
            }
        }

        private bool RateLimitExceeded(string email, string ipAddress)
        {

            int rateLimitThreshold = 3;
            TimeSpan rateLimitDuration = TimeSpan.FromHours(1);

            // Get the current time
            DateTime currentTime = DateTime.UtcNow;

            // Calculate the time threshold
            DateTime thresholdTime = currentTime.Subtract(rateLimitDuration);

            // Count the number of failed login attempts within the threshold time window
            int failedAttemptsCount = _dbContext.FailedLoginAttempts
                .Count(a => a.Email == email && a.IPAddress == ipAddress && a.AttemptTime >= thresholdTime);

            // Check if the count exceeds the rate limit threshold
            return failedAttemptsCount >= rateLimitThreshold;
        }

        private void IncrementFailedLoginAttempts(string email, string ipAddress)
        {
            // Logic to increment failed login attempts for a given email and IP address
            // You can store the number of login attempts in a database or cache
            FailedLoginAttempt failedAttempt = new FailedLoginAttempt
            {
                Email = email,
                IPAddress = ipAddress,
                AttemptTime = DateTime.UtcNow
            };

            _dbContext.FailedLoginAttempts.Add(failedAttempt);
            _dbContext.SaveChanges();
        }

        private void LogAuditTrail(string method, string path, string email, string ipAddress)
        {
            // Log the audit trail in the database or any other desired logging mechanism
            AuditTrail auditTrail = new AuditTrail
            {
                Method = method,
                Path = path,
                Email = email,
                IPAddress = ipAddress,
                Timestamp = DateTime.UtcNow
            };

            _dbContext.AuditTrails.Add(auditTrail);
            _dbContext.SaveChanges();
        }


        //public async Task<Result> ChangePassword(PasswordChange passwordChange)
        //{
        //    try
        //    {
        //        AppLogger.LogInformation("Trying to change Password");
        //        if (passwordChange.newPassword == passwordChange.oldPassword)
        //        {
        //            AppLogger.LogError("New password and old password do match");
        //            return new ErrorResult { Message = "New password and old password do match" };

        //        }

        //        if (passwordChange.newPassword != passwordChange.confirmPassword)
        //        {
        //            AppLogger.LogError("New password and confirm password do not match");
        //            return new ErrorResult { Message = "New password and confirm password do not match" };

        //        }

        //        if (!IsSecurePassword(passwordChange.newPassword))
        //        {
        //            AppLogger.LogError("New password does not meet complexity requirements");
        //            return new ErrorResult { Message = "New password does not meet complexity requirements" };

        //        }

        //        var hashedOldPasswordInput = HashPassword(passwordChange.oldPassword);
        //        AppLogger.LogInformation("Old password hashed");
        //        // Retrieve the user from the database by ID
        //        var user = await _dbContext.Users.FirstOrDefaultAsync(x => x.Id == passwordChange.userId);


        //        var passwordVerify = VerifyPassword(passwordChange.oldPassword, user.Password);
        //        AppLogger.LogInformation($"the message returned after verifying the old and database password is: {passwordVerify}");

        //        if (user == null)
        //        {
        //            AppLogger.LogError("User not found");
        //            return new ErrorResult { Message = "User not found" };

        //        }
        //        if (!passwordVerify)
        //        {
        //            return new ErrorResult { Message = "old password does not match" };
        //        }


        //        //var elapsedSinceLastChange = DateTime.Now - user.PasswordLastChanged;
        //        //if (elapsedSinceLastChange > user.PasswordLifespan)
        //        //{
        //        //    return new SuccessResult { Message = "Password expired, kindly change your password" };
        //        //}

        //        user.Password = HashPassword(passwordChange.newPassword);
        //        user.DateLastUpdated = DateTime.Now;
        //        user.PasswordLastChanged = DateTime.Now;
        //        user.PasswordChanged = true;

        //        await _dbContext.SaveChangesAsync();
        //        AppLogger.LogInformation("Password changed successfully");

        //        return new SuccessResult { Message = "Password changed successfully" };
        //    }
        //    catch (Exception ex)
        //    {
        //        // return new ErrorResult($"{ex.Message}");
        //        AppLogger.LogInformation($"{ex.Message}");
        //        throw;
        //    }

        //}


        private bool IsSecurePassword(string password)
        {

            return
                password.Length >= 8 &&
                password.Any(char.IsUpper) &&
                password.Any(char.IsLower) &&
                password.Any(char.IsDigit) &&
                password.Any(ch => !char.IsLetterOrDigit(ch));
        }

        public async Task<bool> SendTokenByEmail(string username, string token)
        {
            var Sender = Util.AppConfiguration().GetSection("Settings").GetSection("Sender").Value;
            string subject = "Authentication Token";
            string firstName = username.Split('@')[0];
            string body = $"Hello! {firstName},\n\nYour authentication token is: {token}";
            var mail = new SendMailModel()
            {
                Subject = subject,
                Body = body,
                To = username,
                From = Sender
            };
            await SendInstanceEmail(mail);
            return true;

        }


        private async Task LogUserActivity(string username, string actionType, string requestUrl, int statusCode, string ipAddress)
        {
            var activity = new UserActivity
            {
                ActivityId = Guid.NewGuid().ToString(),
                UserName = username,
                Timestamp = DateTime.UtcNow,
                IpAddress = ipAddress ?? "Unknown",
                StatusCode = statusCode,
                ActionType = actionType,
                RequestUrl = requestUrl
            };

            _dbContext.UserActivities.Add(activity);
            await _dbContext.SaveChangesAsync();
        }

        private string GenerateToken(string username)
        {
            try
            {
                Random random = new Random();
                int sixDigitToken = random.Next(100000, 999999);

                string token = sixDigitToken.ToString();

                //// Store the token and expiration time in the database
                _dbContext.Tokens.Add(new Tokens { Username = username, Token = token, ExpirationTime = DateTime.Now.AddMinutes(5) });
                _dbContext.SaveChanges();

                return token;
            }
            catch (Exception ex)
            {
                AppLogger.LogInformation($"{ex.Message}");
                throw;
            }
        }

        public async Task<UserProfile> GetUserByEmailAsync(string email)
        {
            try
            {

                var user = await _dbContext.Users
                    .FirstOrDefaultAsync(u => u.Email == email);
                AppLogger.LogInformation($"user retrieved");

                return user;
            }
            catch (Exception ex)
            {
                AppLogger.LogError($"{ex.Message}");
                throw;
            }
        }
        public async Task<object> UpdateUserAsync(UserProfileUpdate profileUpdate)
        {
            try
            {
                var existingUser = await GetUserByEmailAsync(profileUpdate.Email);
                if (existingUser == null)
                {
                    return new SuccessResult { Message = "Record does not exist!" };
                }

                var auditLog = new UserActivity
                {
                    ActivityId = Guid.NewGuid().ToString(),
                    UserName = existingUser.Email,
                    ActionType = "POST",
                    RequestUrl = "api/Update-user",
                    Timestamp = DateTime.UtcNow.AddHours(1),
                    IpAddress = GetClientIpAddress(),
                    StatusCode = 200,
                };

                _dbContext.UserActivities.Add(auditLog);
                await _dbContext.SaveChangesAsync();

                var n = profileUpdate.Adapt<UserProfile>();
                existingUser.FullName = profileUpdate.FullName;
                //existingUser.FintechName = profileUpdate.FintechName;
                existingUser.IsActive = profileUpdate.IsActive;
                //existingUser.PrimaryAccountNum = profileUpdate.PrimaryAccountNum;
                existingUser.RoleType = profileUpdate.RoleType;

                existingUser.UpdatedBy = profileUpdate.updatedBy;
                //var user = profileUpdate.Adapt<UserProfile>();
                //_dbContext.Users.Attach(user);
                //_dbContext.Entry(user).State = EntityState.Modified;
                await _dbContext.SaveChangesAsync();
                return new SuccessResult { Message = "User updated successfully" };

            }
            catch (Exception ex)
            {
                AppLogger.LogInformation($"{ex.Message}");
                throw;
            }
        }

        public string GetClientIpAddress()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null)
            {
                return "No HTTP context"; // Or throw an exception if needed
            }

            // Check for proxy headers (e.g., X-Forwarded-For)
            if (httpContext.Request.Headers.TryGetValue("X-Forwarded-For", out var forwardedIp))
            {
                return forwardedIp.FirstOrDefault()?.Split(',').FirstOrDefault()?.Trim();
            }

            // Fall back to the direct remote IP
            return httpContext.Connection.RemoteIpAddress?.MapToIPv4().ToString() ?? "Unknown";
        }


        public bool ValidateToken(string username, string token)
        {
            try
            {
                var user = _dbContext.Users.FirstOrDefault(u => u.Email == username);

                if (user == null)
                {
                    return false;
                }

                // Replace this with your actual token retrieval logic from the database
                var tokenData = _dbContext.Tokens.FirstOrDefault(t => t.Username == username);

                if (tokenData == null)
                {
                    return false;
                }
                TimeSpan timeDifference = tokenData.ExpirationTime - DateTime.Now;
                if (timeDifference.TotalMinutes >= 1)
                {
                    return token.Equals(tokenData.Token, StringComparison.OrdinalIgnoreCase);
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                AppLogger.LogInformation($"{ex.Message}");
                throw;
            }

        }



        public async Task<bool> SendInstanceEmail(SendMailModel data)
        {
            try
            {

                string EmailApiBaseUrl = Util.AppConfiguration().GetSection("ExternalUrl").GetSection("EmailApiBaseUrl").Value;
                string SendEmail = Util.AppConfiguration().GetSection("ExternalUrl").GetSection("SendEmail").Value;

                JsonSerializerSettings jsonSerializerSettings = new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                };


                RestClient restClient = new RestClient(EmailApiBaseUrl); // TODO
                restClient.RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;

                RestRequest request = new RestRequest(SendEmail, Method.POST);
                request.AddHeader("Accept", "application/json");

                string jsonObject = JsonConvert.SerializeObject(data, Formatting.Indented, jsonSerializerSettings);
                request.AddParameter("application/json", jsonObject, ParameterType.RequestBody);

                TaskCompletionSource<IRestResponse> taskCompletion = new TaskCompletionSource<IRestResponse>();

                RestRequestAsyncHandle handle = restClient.ExecuteAsync(request, r => taskCompletion.SetResult(r));

                RestResponse response = (RestResponse)(await taskCompletion.Task);
                //AppLogger.LogInformation($"{response}");

                if (response.IsSuccessful)
                {
                    var result = JsonConvert.DeserializeObject<string>(response.Content, new ExpandoObjectConverter());
                    var r = result;

                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception ex)
            {
                AppLogger.LogInformation($"{ex.Message}");
                return false;
            }
        }

        //private string generateJwtToken(UserProfileModel user)
        //{
        //    string _appKey = _configuration.GetSection("JwtSettings").GetSection("SecretKey").Value;
        //    var tokenHandler = new JwtSecurityTokenHandler();
        //    var key = Encoding.ASCII.GetBytes(_appKey);
        //    var tokenDescriptor = new SecurityTokenDescriptor
        //    {
        //        Subject = new ClaimsIdentity(new Claim[]
        //        {
        //            new Claim("UserId", user.Id.ToString()),
        //            new Claim("Email", user.Email),
        //            new Claim("Name", user.FullName),
        //            new Claim("Role", user.RoleType),
        //            new Claim("IsInternal", user.IsActive.ToString())
        //}),
        //        Expires = DateTime.UtcNow.AddDays(1), //.AddMinutes(15),
        //        // Expires = DateTime.UtcNow.AddSeconds(10),
        //        SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
        //    };
        //    var token = tokenHandler.CreateToken(tokenDescriptor);
        //    return tokenHandler.WriteToken(token);
        //}


        //public async Task<Result> VerifyTokenAsync(TokenVerificationRequest request)
        //{
        //    try
        //    {
        //        var tokenData = _dbContext.Tokens.FirstOrDefault(t => t.Username == request.Username);



        //        // Fetch user details
        //        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Email == request.Username);
        //        if (user == null)
        //        {
        //            return new ErrorResult { Message = "User not found" };
        //        }
        //        TimeSpan timeDifference = tokenData.ExpirationTime - DateTime.Now;
        //        if (timeDifference.TotalMinutes < 1)
        //        {
        //            AppLogger.LogError("Token expired");
        //            return new ErrorResult { Message = "Token expired" };
        //        }
        //        var userModel = user.Adapt<UserProfileModel>();

        //        // Generate JWT
        //        var jwtToken = generateJwtToken(userModel);

        //        return new SuccessResult
        //        {
        //            Message = "Login successful",
        //            Content = new { Token = jwtToken, success = true, Email = userModel.Email, FullName = userModel.FullName, Role = userModel.RoleType }
        //        };
        //    }
        //    catch (Exception ex)
        //    {
        //        AppLogger.LogError($"Token verification error: {ex.Message}");
        //        return new ErrorResult { Message = "An error occurred while verifying token" };
        //    }
        //}

        //public async Task<Result> VerifyTokenAsync(TokenVerificationRequest request)
        //{
        //    try
        //    {
        //        if (request == null || string.IsNullOrWhiteSpace(request.Username) || string.IsNullOrWhiteSpace(request.Token))
        //            return new ErrorResult { Message = "Username and token are required." };

        //        // Normalize email (mirror your login rule)
        //        var email = request.Username.Trim().ToLowerInvariant();
        //        if (!email.Contains("@")) email += "@wemabank.com";

        //        // Get token record for this user
        //        var tokenData = await _dbContext.Tokens
        //                        .Where(t => t.Username.ToLower() == email)
        //                        .OrderByDescending(t => t.ExpirationTime)
        //                        .FirstOrDefaultAsync();


        //        if (tokenData == null)
        //            return new ErrorResult { Message = "No active token found. Please request a new token." };

        //        // Expiry check (keep consistent with where you set ExpirationTime = DateTime.Now.AddMinutes(x))
        //        if (tokenData.ExpirationTime <= DateTime.Now)
        //            return new ErrorResult { Message = "Token expired. Please request a new token." };

        //        // Compare the provided token with stored one (constant-time)
        //        var provided = request.Token.Trim();
        //        if (!SecureEquals(provided, tokenData.Token))
        //            return new ErrorResult { Message = "Invalid token." };

        //        // Fetch active user
        //        var user = await _dbContext.Users
        //            .FirstOrDefaultAsync(u => u.Email.ToLower() == email && u.IsActive);

        //        if (user == null)
        //            return new ErrorResult { Message = "User not found or deactivated." };

        //        var userModel = user.Adapt<UserProfileModel>();
        //        var jwtToken = generateJwtToken(userModel);

        //        // Invalidate the token (one-time use)
        //        _dbContext.Tokens.Remove(tokenData);
        //        await _dbContext.SaveChangesAsync();

        //        return new SuccessResult
        //        {
        //            Message = "Login successful",
        //            Content = new
        //            {
        //                Token = jwtToken,
        //                Success = true,
        //                Email = userModel.Email,
        //                FullName = userModel.FullName,
        //                Role = userModel.RoleType
        //            }
        //        };
        //    }
        //    catch (Exception ex)
        //    {
        //        AppLogger.LogError($"Token verification error: {ex.Message}");
        //        return new ErrorResult { Message = "An error occurred while verifying token." };
        //    }
        //}

        //    private string generateJwtToken(UserProfileModel user)
        //    {
        //        string secretKey = _configuration["JwtSettings:SecretKey"];
        //        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey));
        //        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        //        var claims = new[]
        //        {
        //    new Claim(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
        //    new Claim(JwtRegisteredClaimNames.Email, user.Email),
        //    new Claim(ClaimTypes.Name, user.FullName),
        //    new Claim(ClaimTypes.Role, user.RoleType), // 👈 proper role claim
        //    new Claim("IsInternal", user.IsActive.ToString()),
        //    new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
        //};

        //        var token = new JwtSecurityToken(
        //            issuer: _configuration["JwtSettings:Issuer"],
        //            audience: _configuration["JwtSettings:Audience"],
        //            claims: claims,
        //            expires: DateTime.UtcNow.AddMinutes(5), // short-lived access token
        //            signingCredentials: creds
        //        );

        //        return new JwtSecurityTokenHandler().WriteToken(token);
        //    }


        private string generateJwtToken(UserProfileModel user)
        {
            string appKey = _configuration["JwtSettings:SecretKey"];
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(appKey);

            var claims = new[]
            {
        new Claim("UserId", user.Id.ToString()),
        new Claim("Email", user.Email),
        new Claim("Name", user.FullName),
        new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
    };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddMinutes(5),
                Audience = _configuration["JwtSettings:Audience"],
                Issuer = _configuration["JwtSettings:Issuer"],
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }




        private RefreshToken GenerateRefreshToken(string ipAddress)
        {
            return new RefreshToken
            {
                Token = Convert.ToBase64String(RandomNumberGenerator.GetBytes(64)), // cryptographically secure
                Expires = DateTime.UtcNow.AddDays(7), // valid for 7 days
                Created = DateTime.UtcNow,
                CreatedByIp = ipAddress
            };
        }


        public async Task<Result> VerifyTokenAsync(TokenVerificationRequest request)
        {
            try
            {
                var ipAddress = _httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString();
                if (request == null || string.IsNullOrWhiteSpace(request.Username) || string.IsNullOrWhiteSpace(request.Token))
                    return new ErrorResult { Message = "Username and token are required." };

                var email = request.Username.Trim().ToLowerInvariant();
                if (!email.Contains("@")) email += "@wemabank.com";

                // Get most recent valid token
                var tokenData = await _dbContext.Tokens
                    .Where(t => t.Username.ToLower() == email && !t.IsUsed)
                    .OrderByDescending(t => t.ExpirationTime)
                    .FirstOrDefaultAsync();

                if (tokenData == null)
                    return new ErrorResult { Message = "No active token found. Please request a new token." };

                // Expiry check
                if (tokenData.ExpirationTime <= DateTime.UtcNow)
                    return new ErrorResult { Message = "Token expired. Please request a new token." };

                // Constant-time comparison
                var provided = request.Token.Trim();
                if (!SecureEquals(provided, tokenData.Token))
                {
                    await LogUserActivity(email, "OTP Verification Failed", "/api/auth/verifytoken", 401, _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString());
                    return new ErrorResult { Message = "Invalid token." };
                }

                // Validate nonce if present (anti-replay)
                //if (!string.IsNullOrEmpty(tokenData.Nonce) && tokenData.Nonce != request.Nonce)
                //    return new ErrorResult { Message = "Invalid or reused token (replay detected)." };

                // User lookup
                var user = await _dbContext.Users
                    .FirstOrDefaultAsync(u => u.Email.ToLower() == email && u.IsActive);

                if (user == null)
                    return new ErrorResult { Message = "User not found or deactivated." };

                var userModel = user.Adapt<UserProfileModel>();
                var jwtToken = generateJwtToken(userModel);
                var refreshToken = GenerateRefreshToken(ipAddress);

                _dbContext.RefreshTokens.Add(refreshToken);

                // Mark token as used (single-use)
                tokenData.IsUsed = true;
                tokenData.UsedAt = DateTime.UtcNow;
                await _dbContext.SaveChangesAsync();

                // Audit log success
                await LogUserActivity(email, "Login Successful", "/api/auth/verifytoken", 200, _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString());

                return new SuccessResult
                {
                    Message = "Login successful",
                    Content = new
                    {
                        Token = jwtToken,
                        RefreshToken = refreshToken.Token,   // 👈 return this to frontend
                        Success = true,
                        Email = userModel.Email,
                        //FullName = userModel.FullName,
                        //Role = userModel.RoleType
                    }
                };
            }
            catch (Exception ex)
            {
                AppLogger.LogError($"Token verification error: {ex.Message}");
                return new ErrorResult { Message = "An error occurred while verifying token." };
            }
        }


        // Constant-time string equality
        private static bool SecureEquals(string a, string b)
        {
            if (a == null || b == null) return false;
            var aBytes = Encoding.UTF8.GetBytes(a);
            var bBytes = Encoding.UTF8.GetBytes(b);
            if (aBytes.Length != bBytes.Length) return false;
            return CryptographicOperations.FixedTimeEquals(aBytes, bBytes);
        }



    }

    public class UserProfileModel
    {
        public string Id { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }

        public string RoleType { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime DateLastUpdated { get; set; }
    }
}
