using BIDConfirmation.Model;
using BidConfirmationAPI.Model;

namespace BidConfirmationAPI.Interfaces
{
    public interface IUserAccountService
    {
        Task<UserAccountDetails?> GetUserDetailsByAccountNoAsync(string accountNo);
        Task<KachasiDetails?> GetKachasiDetailsAsync(string FormNumber);
        //Task SaveBidRequestAsync(BidRequest bidRequest);
       // Task<KachasiDetails?> GetKachasiDetailAsync(string PickupListID, string FormNumber);
    }
}