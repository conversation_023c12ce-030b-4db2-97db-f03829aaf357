# Troubleshooting Guide

## Overview

This guide provides solutions to common issues, debugging procedures, and diagnostic steps for the BID Confirmation Portal API. It covers application errors, database issues, authentication problems, and performance concerns.

## Common Issues and Solutions

### 1. Application Startup Issues

#### Issue: Application fails to start
**Symptoms:**
- Application crashes on startup
- Port binding errors
- Configuration errors

**Diagnostic Steps:**
```bash
# Check if ports are available
netstat -an | findstr :5056
netstat -an | findstr :7011

# Verify .NET runtime
dotnet --version

# Check application logs
type "C:\Logs\BIDCONFIRMATION\internallog.txt"
```

**Solutions:**
1. **Port Conflicts:**
   ```json
   // Update launchSettings.json
   {
     "applicationUrl": "https://localhost:7012;http://localhost:5057"
   }
   ```

2. **Missing Dependencies:**
   ```bash
   dotnet restore
   dotnet build
   ```

3. **Configuration Issues:**
   - Verify `appsettings.json` syntax
   - Check connection strings
   - Validate JWT settings

#### Issue: SSL Certificate errors
**Error Message:** "Unable to configure HTTPS endpoint"

**Solutions:**
1. **Development Certificate:**
   ```bash
   dotnet dev-certs https --clean
   dotnet dev-certs https --trust
   ```

2. **Production Certificate:**
   - Install valid SSL certificate
   - Configure certificate in IIS
   - Update application configuration

### 2. Database Connection Issues

#### Issue: Database connection failures
**Error Messages:**
- "A network-related or instance-specific error occurred"
- "Login failed for user"
- "Cannot open database"

**Diagnostic Steps:**
```bash
# Test SQL Server connectivity
sqlcmd -S server_name -U username -P password

# Check SQL Server service
sc query MSSQLSERVER

# Verify connection string
```

**Solutions:**
1. **Connection String Issues:**
   ```json
   {
     "ConnectionStrings": {
       "BID_CONFIRMATIONDB": "server=localhost\\SQLEXPRESS;Database=BID_CONFIRMATION;Integrated Security=true;TrustServerCertificate=True"
     }
   }
   ```

2. **SQL Server Not Running:**
   ```bash
   # Start SQL Server service
   net start MSSQLSERVER
   ```

3. **Database Permissions:**
   ```sql
   -- Grant permissions to application user
   USE BID_CONFIRMATION;
   CREATE USER [app_user] FOR LOGIN [app_user];
   ALTER ROLE db_datareader ADD MEMBER [app_user];
   ALTER ROLE db_datawriter ADD MEMBER [app_user];
   ```

#### Issue: Migration failures
**Error Messages:**
- "The database does not exist"
- "Migration already applied"
- "Column already exists"

**Solutions:**
1. **Create Database:**
   ```bash
   dotnet ef database update
   ```

2. **Reset Migrations:**
   ```bash
   # Remove migration files
   dotnet ef migrations remove

   # Create new initial migration
   dotnet ef migrations add InitialCreate
   dotnet ef database update
   ```

### 3. Authentication and Authorization Issues

#### Issue: JWT token validation failures
**Error Messages:**
- "401 Unauthorized"
- "Invalid token"
- "Token expired"

**Diagnostic Steps:**
```csharp
// Check token in logs
_logger.LogInformation("Token received: {Token}", token);

// Verify JWT settings
var jwtSettings = _configuration.GetSection("JwtSettings");
_logger.LogInformation("JWT Issuer: {Issuer}", jwtSettings["Issuer"]);
```

**Solutions:**
1. **Token Expiry:**
   ```json
   {
     "JwtSettings": {
       "ExpiryMinutes": 30
     }
   }
   ```

2. **Secret Key Mismatch:**
   - Ensure same secret key across environments
   - Verify key length (minimum 256 bits)

3. **Clock Skew:**
   ```csharp
   options.TokenValidationParameters = new TokenValidationParameters
   {
       ClockSkew = TimeSpan.FromMinutes(5)
   };
   ```

#### Issue: LDAP authentication failures
**Error Messages:**
- "LDAP server not available"
- "Invalid credentials"
- "Connection timeout"

**Diagnostic Steps:**
```bash
# Test LDAP connectivity
telnet ldap-server-ip 389

# Check LDAP configuration
```

**Solutions:**
1. **Network Connectivity:**
   - Verify LDAP server IP and port
   - Check firewall settings
   - Test network connectivity

2. **Configuration Issues:**
   ```json
   {
     "ExternalUrl": {
       "LDapServerIP": "correct-ldap-ip",
       "LDapServerPort": "389"
     }
   }
   ```

### 4. API Endpoint Issues

#### Issue: 403 Forbidden errors
**Symptoms:**
- Users cannot access endpoints after 3:00 PM
- Role-based access denied

**Diagnostic Steps:**
```csharp
// Check current time and user role
_logger.LogInformation("Current time: {Time}, User role: {Role}", 
    DateTime.Now.TimeOfDay, userRole);
```

**Solutions:**
1. **Time Restriction:**
   - Verify CutoffTimeMiddleware configuration
   - Check system time settings
   - Review business rules

2. **Role Authorization:**
   ```csharp
   // Verify user role in database
   var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
   _logger.LogInformation("User role from DB: {Role}", user?.RoleType);
   ```

#### Issue: File upload failures
**Error Messages:**
- "Request entity too large"
- "Invalid file format"
- "File upload timeout"

**Solutions:**
1. **File Size Limits:**
   ```json
   // In appsettings.json
   {
     "Kestrel": {
       "Limits": {
         "MaxRequestBodySize": 52428800
       }
     }
   }
   ```

2. **File Type Validation:**
   ```csharp
   var allowedTypes = new[] { "application/pdf", "image/jpeg", "image/png" };
   if (!allowedTypes.Contains(file.ContentType))
   {
       return BadRequest("Invalid file type");
   }
   ```

### 5. Performance Issues

#### Issue: Slow API responses
**Symptoms:**
- High response times
- Database timeouts
- Memory usage spikes

**Diagnostic Steps:**
```csharp
// Add performance logging
var stopwatch = Stopwatch.StartNew();
var result = await _service.ProcessAsync(request);
stopwatch.Stop();
_logger.LogInformation("Operation completed in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
```

**Solutions:**
1. **Database Query Optimization:**
   ```csharp
   // Use specific columns and filtering
   var users = await _context.Users
       .Where(u => u.IsActive)
       .Select(u => new { u.Id, u.Email })
       .ToListAsync();
   ```

2. **Connection Pooling:**
   ```csharp
   services.AddDbContext<ApplicationDbContext>(options =>
       options.UseSqlServer(connectionString, sqlOptions =>
           sqlOptions.CommandTimeout(30)));
   ```

3. **Async Operations:**
   ```csharp
   // Use async/await properly
   public async Task<Result> ProcessAsync()
   {
       var task1 = _service1.ProcessAsync();
       var task2 = _service2.ProcessAsync();
       
       await Task.WhenAll(task1, task2);
       
       return new SuccessResult();
   }
   ```

## Logging and Diagnostics

### Log Locations

**Application Logs:**
- Location: `C:\Logs\BIDCONFIRMATION\`
- Format: `YYYY-MM-DD_logfile.txt`
- Content: Application events, errors, and debug information

**IIS Logs (if using IIS):**
- Location: `C:\inetpub\logs\LogFiles\W3SVC1\`
- Format: W3C Extended Log Format
- Content: HTTP requests and responses

**Windows Event Logs:**
- Application Log: Application-specific events
- System Log: System-level events
- Security Log: Authentication events

### Log Analysis

**Common Log Patterns:**
```bash
# Find authentication errors
findstr "Authentication failed" C:\Logs\BIDCONFIRMATION\*.txt

# Find database errors
findstr "SqlException" C:\Logs\BIDCONFIRMATION\*.txt

# Find performance issues
findstr "timeout" C:\Logs\BIDCONFIRMATION\*.txt
```

**Log Level Configuration:**
```xml
<!-- nlog.config -->
<rules>
  <!-- Debug level for development -->
  <logger name="*" minlevel="Debug" writeTo="logfile" />
  
  <!-- Information level for production -->
  <logger name="*" minlevel="Info" writeTo="logfile" />
</rules>
```

### Debugging Procedures

#### 1. Application Debugging

**Visual Studio Debugging:**
1. Set breakpoints in code
2. Start debugging (F5)
3. Step through code execution
4. Examine variable values
5. Check call stack

**Remote Debugging:**
1. Install Remote Debugging Tools on server
2. Configure firewall exceptions
3. Attach Visual Studio to remote process
4. Debug as normal

#### 2. Database Debugging

**SQL Server Profiler:**
1. Start SQL Server Profiler
2. Create new trace
3. Select events to monitor
4. Run application operations
5. Analyze captured queries

**Query Performance:**
```sql
-- Check slow queries
SELECT TOP 10 
    total_elapsed_time/execution_count AS avg_elapsed_time,
    text
FROM sys.dm_exec_query_stats 
CROSS APPLY sys.dm_exec_sql_text(sql_handle)
ORDER BY avg_elapsed_time DESC;
```

#### 3. Network Debugging

**Connection Testing:**
```bash
# Test HTTP endpoints
curl -X GET "https://localhost:7011/api/useraccount/details/123" -H "Authorization: Bearer token"

# Test database connectivity
telnet database-server 1433

# Test LDAP connectivity
telnet ldap-server 389
```

## Emergency Procedures

### Application Recovery

**Service Restart:**
```bash
# IIS Application Pool restart
iisreset /restart

# Windows Service restart
net stop "BidConfirmationAPI"
net start "BidConfirmationAPI"
```

**Database Recovery:**
```sql
-- Check database status
SELECT name, state_desc FROM sys.databases WHERE name = 'BID_CONFIRMATION';

-- Restore from backup if needed
RESTORE DATABASE BID_CONFIRMATION FROM DISK = 'backup_path.bak';
```

### Rollback Procedures

**Application Rollback:**
1. Stop application service
2. Replace application files with previous version
3. Rollback database migrations if needed
4. Update configuration files
5. Restart application service

**Database Rollback:**
```bash
# Rollback to specific migration
dotnet ef database update PreviousMigrationName
```

## Monitoring and Alerts

### Health Checks

**Application Health:**
```csharp
// Add health checks
services.AddHealthChecks()
    .AddDbContext<ApplicationDbContext>()
    .AddCheck("ldap", () => TestLdapConnection())
    .AddCheck("email", () => TestEmailService());
```

**Database Health:**
```sql
-- Check database performance
SELECT 
    cntr_value as 'Page Life Expectancy'
FROM sys.dm_os_performance_counters 
WHERE counter_name = 'Page life expectancy';
```

### Performance Monitoring

**Key Metrics:**
- Response time percentiles
- Error rates
- Memory usage
- CPU utilization
- Database connection pool usage

**Alerting Thresholds:**
- Response time > 5 seconds
- Error rate > 5%
- Memory usage > 80%
- Failed login attempts > 10/hour

This troubleshooting guide provides comprehensive coverage for diagnosing and resolving issues in the BID Confirmation Portal API system.
