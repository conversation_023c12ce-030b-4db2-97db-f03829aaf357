﻿using BidConfirmationAPI.Data;
using BidConfirmationAPI.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using System;

namespace BIDConfirmation
{
    public class RoleRequirement : IAuthorizationRequirement
    {
        public IReadOnlyList<string> AllowedRoles { get; }
        public RoleRequirement(params string[] roles)
        {
            AllowedRoles = roles ?? Array.Empty<string>();
        }
    }


    public class RoleHandler : AuthorizationHandler<RoleRequirement>
    {
        private readonly ApplicationDbContext _dbContext;
        private readonly ILogger<RoleHandler> _logger;

        public RoleHandler(ApplicationDbContext dbContext, ILogger<RoleHandler> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        protected override async Task HandleRequirementAsync(
            AuthorizationHandlerContext context,
            RoleRequirement requirement)
        {
            try
            {
                // Extract UserId from token claims
                var userId = context.User.FindFirst("UserId")?.Value;

                if (string.IsNullOrWhiteSpace(userId))
                {
                    AppLogger.LogError("Authorization failed: Missing UserId claim.");
                    context.Fail();
                    return;
                }

                // Fetch user from DB (string PK)
                var user = await _dbContext.Users
                    .AsNoTracking()
                    .FirstOrDefaultAsync(u => u.Id == userId);

                if (user == null)
                {
                    AppLogger.LogError($"Authorization failed: User with ID {userId} not found.");
                    context.Fail();
                    return;
                }

                if (!user.IsActive)
                {
                    AppLogger.LogError($"Authorization failed: User {userId} is inactive.");
                    context.Fail();
                    return;
                }


                if (requirement.AllowedRoles.Contains(user.RoleType, StringComparer.OrdinalIgnoreCase))
                {
                    context.Succeed(requirement);
                }
                else
                {
                    // Log possible tampering attempt
                    var tokenRole = context.User.FindFirst("Role")?.Value;
                    AppLogger.LogError($"Role mismatch detected for user {userId}. Token Role: {tokenRole}");


                    context.Fail();
                }
            }
            catch (Exception ex)
            {
                AppLogger.LogError($"Role authorization failed for requirement {ex}.");
                context.Fail();
            }
        }
    }
    //public class RoleRequirement : IAuthorizationRequirement
    //{
    //    public IReadOnlyList<string> AllowedRoles { get; }
    //    public RoleRequirement(params string[] roles)
    //    {
    //        AllowedRoles = roles;
    //    }
    //}


    //public class RoleHandler : AuthorizationHandler<RoleRequirement>
    //{
    //    private readonly ApplicationDbContext _context;
    //    private readonly IHttpContextAccessor _httpContextAccessor;

    //    public RoleHandler(ApplicationDbContext context, IHttpContextAccessor httpContextAccessor)
    //    {
    //        _context = context;
    //        _httpContextAccessor = httpContextAccessor;
    //    }

    //    protected override async Task HandleRequirementAsync(
    //        AuthorizationHandlerContext context,
    //        RoleRequirement requirement)
    //    {
    //        var userIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst("UserId")?.Value;
    //        if (userIdClaim == null)
    //        {
    //            context.Fail();
    //            return;
    //        }

    //        if (!Guid.TryParse(userIdClaim, out var userId))
    //        {
    //            context.Fail();
    //            return;
    //        }

    //        var user = await _context.Users.FindAsync(userId);
    //        if (user == null)
    //        {
    //            context.Fail();
    //            return;
    //        }

    //        // ✅ Check if user role is in allowed list
    //        if (requirement.AllowedRoles.Contains(user.RoleType, StringComparer.OrdinalIgnoreCase))
    //        {
    //            context.Succeed(requirement);
    //        }
    //        else
    //        {
    //            // 🚨 Log mismatch attempt
    //            AppLogger.LogError($"[SECURITY ALERT] User {user.Email} ({user.RoleType}) attempted to access {string.Join(",", requirement.AllowedRoles)}-only resource.");
    //            context.Fail();
    //        }
    //    }
    //}


}
