﻿namespace BidConfirmationAPI.Models
{
    public class UserProfile
    {
        public UserProfile()
        {
            Id = new Guid().ToString();
            CreatedOn = DateTime.Now;
            DateLastUpdated = DateTime.Now;
            //PasswordLifespan = 90;
            //IsDeleted = false;
        }
        public string Id { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        //public string? Password { get; set; }
        //public bool IsInternal { get; set; }

        //public string? PrimaryAccountNum { get; set; }
        public string RoleType { get; set; }


        //public TimeSpan? PasswordLifespan { get; set; }

        //public DateTime? PasswordLastChanged { get; set; }
        //public bool PasswordChanged { get; set; }
        public DateTime CreatedOn { get; set; }
        public DateTime DateLastUpdated { get; set; }
        public bool IsActive { get; set; }
        public string CreatedBy { get; set; }
        public string UpdatedBy { get; set; }

        //public string GenerateAutoPassword()
        //{
        //    const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        //    const int passwordLength = 12;

        //    var random = new Random();
        //    Password = new string(Enumerable.Repeat(chars, passwordLength)
        //      .Select(s => s[random.Next(s.Length)]).ToArray());
        //    return Password;
        //}
    }
}
