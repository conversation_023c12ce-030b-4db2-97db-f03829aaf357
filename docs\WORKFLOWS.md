# Business Workflows

## Overview

The BID Confirmation Portal manages the complete lifecycle of Form M applications for foreign exchange transactions. The system implements a multi-stage approval workflow with role-based responsibilities and automated notifications.

## Workflow Diagram

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│     RM      │    │    TRADE    │    │  TREASURY   │    │   SYSTEM    │
│ Relationship│    │    TEAM     │    │    TEAM     │    │  ACTIONS    │
│  Manager    │    │             │    │             │    │             │
└─────┬───────┘    └─────┬───────┘    └─────┬───────┘    └─────┬───────┘
      │                  │                  │                  │
      │ 1. Submit Form M │                  │                  │
      ├─────────────────►│                  │                  │
      │                  │                  │                  │ 2. Email
      │                  │                  │                  │ Notification
      │                  │                  │                  │◄─────────
      │                  │                  │                  │
      │                  │ 3. Review & Approve/Reject          │
      │                  ├─────────────────►│                  │
      │                  │                  │                  │ 4. <PERSON><PERSON>
      │                  │                  │                  │ Notification
      │                  │                  │                  │◄─────────
      │                  │                  │                  │
      │                  │                  │ 5. Final Processing│
      │                  │                  ├─────────────────►│
      │                  │                  │                  │
      │ 6. Status Updates│                  │                  │
      │◄─────────────────┼─────────────────┼─────────────────┤
```

## User Roles and Responsibilities

### 1. Relationship Manager (RM)
**Primary Responsibilities:**
- Submit Form M applications with supporting documents
- Gather customer information and validate data
- Ensure compliance with regulatory requirements
- Monitor application status

**Key Activities:**
- Account information lookup
- Form M data entry
- Document upload
- Application submission
- Status tracking

**Time Restrictions:**
- Access limited to business hours (before 3:00 PM)
- Cannot submit applications after cutoff time

### 2. Trade Team
**Primary Responsibilities:**
- Review Form M applications for compliance
- Validate trade documentation
- Approve or reject applications
- Set transaction references

**Key Activities:**
- Application review
- Document verification
- Compliance checking
- Approval/rejection decisions
- Transaction reference assignment

**Time Restrictions:**
- Access limited to business hours (before 3:00 PM)
- Cannot process applications after cutoff time

### 3. Treasury Team
**Primary Responsibilities:**
- Final processing of approved applications
- Exchange rate setting
- Value date determination
- Transaction execution

**Key Activities:**
- Rate determination
- Amount validation
- Final approval
- Transaction processing
- Document download

**Time Restrictions:**
- No time restrictions
- Can process applications 24/7

### 4. Admin
**Primary Responsibilities:**
- User management
- System administration
- Audit monitoring
- Configuration management

**Key Activities:**
- User creation and management
- Role assignment
- System monitoring
- Audit trail review

## Detailed Workflow Processes

### 1. Form M Submission Workflow

#### Step 1: Account Information Lookup
```
RM → System: GET /api/UserAccount/details/{accountNo}
System → Oracle DB: Query account information
System → RM: Return account details (balance, BVN, name)
```

#### Step 2: Form M Information Lookup
```
RM → System: GET /api/UserAccount/kachasiDetails/{FormNumber}
System → External API: Query Form M details
System → RM: Return Form M information
```

#### Step 3: Application Submission
```
RM → System: POST /api/UserAccount/submit-bid
- Applicant details (JSON)
- Supporting documents (files)
- RM email

System Actions:
1. Validate input data
2. Store applicant details in database
3. Store documents as binary data
4. Set status to "Submitted"
5. Create audit trail entry
6. Send email notification to Trade team
```

**Email Notification to Trade Team:**
```
Subject: FORM M REQUEST ON BID CONFIRMATION PORTAL
Body: Dear Team,
      A Form M has been submitted for your approval.
      Please log into BID Confirmation Portal immediately to treat this.
      Best regards.
```

### 2. Trade Review Workflow

#### Trade Team Review Process
```
Trade User → System: GET /api/UserAccount/get-all-requests
System → Trade User: Return pending applications

Trade User → System: GET /api/UserAccount/get-request/{id}
System → Trade User: Return detailed application

Trade User → System: POST /api/UserAccount/trade-review/{id}
Request Body:
{
  "approved": true/false,
  "reviewer": "<EMAIL>",
  "transactionReference": "TXN123456789",
  "reason": "Rejection reason (if rejected)"
}
```

#### Approval Path
```
System Actions (if approved):
1. Update status to "TradeAccepted"
2. Set trade reviewer and review date
3. Store transaction reference
4. Send email notification to Treasury team
5. Create audit trail entry
```

**Email Notification to Treasury (Approval):**
```
Subject: FORM M APPROVED BY TRADE TEAM
Body: Dear Treasury Team,
      A Form M has been approved by the Trade Team.
      Please log into BID Confirmation Portal immediately to treat this.
      Best regards.
```

#### Rejection Path
```
System Actions (if rejected):
1. Update status to "TradeRejected"
2. Set rejection reason
3. Set trade reviewer and review date
4. Send email notification to RM
5. Create audit trail entry
```

**Email Notification to RM (Rejection):**
```
Subject: FORM M REJECTED BY TRADE TEAM
Body: Dear RM,
      A Form M has been rejected by the Trade Team.
      Please log into BID Confirmation Portal immediately to treat this.
      Best regards.
```

### 3. Treasury Processing Workflow

#### Treasury Review Process
```
Treasury User → System: GET /api/UserAccount/get-all-requests?status=TradeAccepted
System → Treasury User: Return trade-approved applications

Treasury User → System: POST /api/UserAccount/treasury-review/{id}
Request Body:
{
  "approved": true/false,
  "reviewer": "<EMAIL>",
  "reason": "Rejection reason (if rejected)",
  "amount": 95000.00,
  "rate": 1500.00,
  "valueDate": "2024-02-15T00:00:00Z"
}
```

#### Final Approval Path
```
System Actions (if approved):
1. Update status to "TreasuryAccepted"
2. Set treasury reviewer and review date
3. Store treated amount, rate, and value date
4. Calculate balance (if applicable)
5. Send email notification to Trade reviewer and RM
6. Create audit trail entry
```

#### Final Rejection Path
```
System Actions (if rejected):
1. Update status to "TreasuryRejected"
2. Set rejection reason
3. Set treasury reviewer and review date
4. Send email notification to Trade reviewer and RM
5. Create audit trail entry
```

**Email Notification (Treasury Decision):**
```
Subject: TREASURY DECISION ON FORM M APPLICATION
Body: Dear Team,
      The Treasury team has [approved/rejected] a Form M application.
      [Rejection Reason: {reason}]
      Please log into the BID Confirmation Portal for further processing.
      Best regards.
```

## Status Transitions

### Application Status Flow

```
Submitted → TradeAccepted → TreasuryAccepted (Final Success)
    ↓           ↓               ↓
TradeRejected  TreasuryRejected (Final Rejection)
```

### Status Definitions

| Status | Description | Next Possible States |
|--------|-------------|---------------------|
| **Submitted** | Initial submission by RM | TradeAccepted, TradeRejected |
| **TradeAccepted** | Approved by Trade team | TreasuryAccepted, TreasuryRejected |
| **TradeRejected** | Rejected by Trade team | Terminal state |
| **TreasuryAccepted** | Final approval by Treasury | Terminal state |
| **TreasuryRejected** | Rejected by Treasury | Terminal state |

## Document Management Workflow

### Document Upload Process
```
1. RM uploads documents during submission
2. System validates file types and sizes
3. Documents stored as binary data in database
4. Document metadata recorded (filename, type, upload date)
5. Documents linked to applicant record
```

### Document Download Process
```
1. Treasury user requests document download
2. System validates user permissions
3. System records download activity
4. Binary data retrieved and served
5. Download audit trail created
```

## Business Rules and Validations

### Time-Based Restrictions
- **RM and Trade users**: Cannot access system after 3:00 PM
- **Cutoff enforcement**: Middleware blocks requests after hours
- **Treasury users**: No time restrictions

### Data Validation Rules
- **Account Number**: Must exist in external system
- **Form M Number**: Must be valid and registered
- **Email Addresses**: Must be valid Wema Bank emails
- **File Uploads**: Supported formats and size limits
- **Currency Amounts**: Must be positive values

### Approval Rules
- **Sequential Approval**: Trade approval required before Treasury
- **Single Reviewer**: One person per approval stage
- **Mandatory Fields**: All required fields must be completed
- **Transaction Reference**: Required for Trade approval

## Notification System

### Email Triggers
1. **Form M Submission**: Notify Trade team
2. **Trade Approval**: Notify Treasury team
3. **Trade Rejection**: Notify RM
4. **Treasury Decision**: Notify Trade reviewer and RM

### Email Configuration
- **Sender**: <EMAIL>
- **Recipients**: Dynamic based on active users in roles
- **Format**: HTML emails with professional formatting

## Audit and Compliance

### Audit Trail
- **User Activities**: All API calls logged
- **Status Changes**: Complete history maintained
- **Document Access**: Download tracking
- **Authentication Events**: Login/logout tracking

### Compliance Features
- **Complete Traceability**: Full audit trail
- **Role Segregation**: Clear separation of duties
- **Time Stamping**: All activities timestamped
- **Data Integrity**: Immutable audit records

## Error Handling and Recovery

### Common Scenarios
- **Network Failures**: Retry mechanisms
- **Email Delivery Issues**: Logging and alerting
- **Database Timeouts**: Connection pooling
- **File Upload Failures**: Validation and error messages

### Recovery Procedures
- **Failed Submissions**: Manual retry capability
- **Lost Notifications**: Admin can resend emails
- **Data Corruption**: Database backup and restore
- **System Downtime**: Graceful degradation

This workflow documentation provides comprehensive guidance for understanding and operating the BID Confirmation Portal business processes.
