# Authentication and Security

## Overview

The BID Confirmation Portal API implements a comprehensive security model with JWT-based authentication, LDAP integration, role-based authorization, and multiple security middleware components to ensure secure access to sensitive financial data.

## Authentication Architecture

### Authentication Flow

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Client    │    │     API     │    │    LDAP     │    │  Database   │
│ Application │    │   Server    │    │   Server    │    │   Server    │
└─────┬───────┘    └─────┬───────┘    └─────┬───────┘    └─────┬───────┘
      │                  │                  │                  │
      │ 1. Login Request │                  │                  │
      ├─────────────────►│                  │                  │
      │                  │ 2. <PERSON><PERSON><PERSON> Auth     │                  │
      │                  ├─────────────────►│                  │
      │                  │ 3. Auth Response │                  │
      │                  │◄─────────────────┤                  │
      │                  │ 4. Generate Token│                  │
      │                  ├─────────────────────────────────────►│
      │ 5. JWT + Refresh │                  │                  │
      │◄─────────────────┤                  │                  │
      │                  │                  │                  │
      │ 6. API Request   │                  │                  │
      ├─────────────────►│ 7. Validate JWT  │                  │
      │                  ├─────────────────────────────────────►│
      │ 8. API Response  │                  │                  │
      │◄─────────────────┤                  │                  │
```

### Two-Factor Authentication Process

1. **Initial Login**: User provides email/password
2. **LDAP Validation**: Credentials verified against Active Directory
3. **Token Generation**: 6-digit token generated and emailed to user
4. **Token Verification**: User submits token for verification
5. **JWT Issuance**: JWT and refresh token issued upon successful verification

## JWT Token Implementation

### Token Structure

```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "UserId": "user-guid",
    "Email": "<EMAIL>",
    "Name": "User Full Name",
    "jti": "token-id",
    "iss": "your-app-name",
    "aud": "your-app-users",
    "exp": 1234567890
  }
}
```

### Token Configuration

```csharp
// JWT Settings in appsettings.json
{
  "JwtSettings": {
    "SecretKey": "256-bit-secret-key",
    "Issuer": "your-app-name",
    "Audience": "your-app-users",
    "ExpiryMinutes": 5  // Short-lived tokens
  }
}
```

### Token Validation

```csharp
services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = configuration["JwtSettings:Issuer"],
        ValidAudience = configuration["JwtSettings:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(
            Encoding.UTF8.GetBytes(configuration["JwtSettings:SecretKey"])
        ),
        RoleClaimType = ClaimTypes.Role
    };
});
```

## LDAP Integration

### LDAP Authentication

```csharp
public string AuthenticateStaff(string staffEmail, string staffPassword)
{
    try
    {
        LdapDirectoryIdentifier ldi = new LdapDirectoryIdentifier(LDapServerIP, LDapServerPort);
        LdapConnection ldapConnection = new LdapConnection(ldi);
        
        ldapConnection.AuthType = AuthType.Basic;
        ldapConnection.SessionOptions.ProtocolVersion = 3;
        NetworkCredential nc = new NetworkCredential(staffEmail, staffPassword);
        
        ldapConnection.Bind(nc);
        return "true";
    }
    catch (LdapException e)
    {
        return e.Message;
    }
}
```

### LDAP Configuration

```json
{
  "ExternalUrl": {
    "LDapServerIP": "***********",
    "LDapServerPort": "389"
  }
}
```

## Role-Based Authorization

### User Roles

| Role | Description | Permissions |
|------|-------------|-------------|
| **Admin** | System Administrator | User management, system configuration |
| **RM** | Relationship Manager | Submit Form M applications, view account details |
| **TRADE** | Trade Team Member | Review and approve/reject applications |
| **TREASURY** | Treasury Team Member | Final processing, rate setting |

### Authorization Policies

```csharp
services.AddAuthorization(options =>
{
    options.AddPolicy("RequireAdminRole", 
        policy => policy.Requirements.Add(new RoleRequirement("Admin")));
    options.AddPolicy("RequireTreasuryRole", 
        policy => policy.Requirements.Add(new RoleRequirement("Treasury")));
    options.AddPolicy("RequireTradeRole", 
        policy => policy.Requirements.Add(new RoleRequirement("Trade")));
    options.AddPolicy("RequireRMRole", 
        policy => policy.Requirements.Add(new RoleRequirement("RM")));
    options.AddPolicy("RequirePrivilegedAccess", 
        policy => policy.Requirements.Add(new RoleRequirement("Admin", "Treasury")));
});
```

### Custom Authorization Handler

```csharp
public class RoleHandler : AuthorizationHandler<RoleRequirement>
{
    protected override async Task HandleRequirementAsync(
        AuthorizationHandlerContext context,
        RoleRequirement requirement)
    {
        var userId = context.User.FindFirst("UserId")?.Value;
        var user = await _dbContext.Users.FirstOrDefaultAsync(u => u.Id == userId);
        
        if (user != null && user.IsActive && 
            requirement.AllowedRoles.Contains(user.RoleType, StringComparer.OrdinalIgnoreCase))
        {
            context.Succeed(requirement);
        }
        else
        {
            context.Fail();
        }
    }
}
```

## Security Middleware

### Cutoff Time Middleware

Enforces business hour restrictions for RM and TRADE roles:

```csharp
public class CutoffTimeMiddleware
{
    public async Task InvokeAsync(HttpContext context)
    {
        if (context.User.Identity.IsAuthenticated)
        {
            var role = context.User.Claims.FirstOrDefault(c => c.Type == "RoleType")?.Value;
            var currentTime = DateTime.Now;

            if ((role == "RM" || role == "TRADE") && 
                currentTime.TimeOfDay > new TimeSpan(15, 0, 0)) // 3:00 PM
            {
                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                await context.Response.WriteAsync("Access denied: Cut-off time (3:00 PM) reached.");
                return;
            }
        }
        await _next(context);
    }
}
```

## Security Features

### Rate Limiting

Protection against brute force attacks:

```csharp
private void IncrementFailedLoginAttempts(string email, string ipAddress)
{
    var attempt = new FailedLoginAttempt
    {
        Email = email,
        IpAddress = ipAddress,
        AttemptTime = DateTime.UtcNow,
        Id = Guid.NewGuid()
    };
    _dbContext.FailedLoginAttempts.Add(attempt);
    _dbContext.SaveChanges();
}

private bool RateLimitExceeded(string email, string ipAddress)
{
    var oneHourAgo = DateTime.UtcNow.AddHours(-1);
    var recentAttempts = _dbContext.FailedLoginAttempts
        .Where(f => (f.Email == email || f.IpAddress == ipAddress) && 
                   f.AttemptTime > oneHourAgo)
        .Count();
    
    return recentAttempts >= 5; // Max 5 attempts per hour
}
```

### Refresh Token Implementation

```csharp
private RefreshToken GenerateRefreshToken(string ipAddress)
{
    return new RefreshToken
    {
        Token = Convert.ToBase64String(RandomNumberGenerator.GetBytes(64)),
        Expires = DateTime.UtcNow.AddDays(7),
        Created = DateTime.UtcNow,
        CreatedByIp = ipAddress
    };
}
```

### Audit Logging

Complete tracking of user activities:

```csharp
private async Task LogUserActivity(string username, string action, string url, int statusCode, string ipAddress)
{
    var activity = new UserActivity
    {
        ActivityId = Guid.NewGuid().ToString(),
        UserName = username,
        ActionType = action,
        RequestUrl = url,
        Timestamp = DateTime.UtcNow,
        IpAddress = ipAddress,
        StatusCode = statusCode
    };
    
    _dbContext.UserActivities.Add(activity);
    await _dbContext.SaveChangesAsync();
}
```

## API Security

### Endpoint Protection

```csharp
[Authorize(Policy = "RequireRMRole")]
[HttpPost("submit-bid")]
public async Task<IActionResult> SubmitBidWithFiles([FromForm] string applicantJson, 
    string RMEmail, [FromForm] List<IFormFile> files)
{
    // Implementation
}

[Authorize(Policy = "RequireTradeRole")]
[HttpPost("trade-review/{id}")]
public async Task<IActionResult> TradeReview(Guid id, [FromBody] ApprovalRequest request)
{
    // Implementation
}
```

### CORS Configuration

```csharp
services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});
```

## Data Protection

### Sensitive Data Handling

- **Passwords**: Never stored in plain text (LDAP authentication)
- **Tokens**: Encrypted and time-limited
- **Personal Data**: Encrypted in database
- **File Storage**: Binary data stored securely in database

### Database Security

- **Connection String Encryption**: Sensitive connection data protected
- **SQL Injection Prevention**: Parameterized queries and Entity Framework
- **Access Control**: Database-level permissions and roles

## Security Best Practices

### Development
- Use HTTPS in all environments
- Implement proper error handling without exposing sensitive information
- Regular security code reviews
- Dependency vulnerability scanning

### Production
- Regular security audits
- Monitor authentication failures
- Implement proper logging and monitoring
- Regular backup and disaster recovery testing

### Compliance
- Audit trail for all financial transactions
- Data retention policies
- Access control documentation
- Regular security assessments

## Security Monitoring

### Log Analysis
- Failed authentication attempts
- Unusual access patterns
- Privilege escalation attempts
- Data access anomalies

### Alerting
- Multiple failed login attempts
- After-hours access attempts
- Unauthorized role access
- System errors and exceptions

This security implementation provides comprehensive protection for the BID Confirmation Portal API while maintaining usability for authorized users.
