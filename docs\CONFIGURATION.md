# Configuration and Deployment

## Overview

This document covers all configuration settings, environment variables, deployment procedures, and operational considerations for the BID Confirmation Portal API.

## Configuration Files

### 1. appsettings.json
**Purpose**: Main application configuration file

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "BID_CONFIRMATIONDB": "server=SERVER_NAME;Database=BID_CONFIRMATION;uid=USERNAME;password=PASSWORD;TrustServerCertificate=True",
    "NIBSSDB": "server=SERVER_NAME;Database=NIBBS_INWARD;uid=USERNAME;password=PASSWORD;",
    "AccountQuery": "select CUSTOM.effbal(acid,'01') as avail_bal,custom.getbvn(foracid) as bvn,lien_amt,acct_name from tbaadm.gam where foracid = :AccountNo"
  },
  "JwtSettings": {
    "SecretKey": "your-256-bit-secret-key-here",
    "Issuer": "your-app-name",
    "Audience": "your-app-users",
    "ExpiryMinutes": 30
  },
  "ExternalUrl": {
    "BaseURL": "https://external-api-url.com/api/v1/external/consent",
    "iWemaBaseUrl": "http://internal-server-ip",
    "iWemaLoginPath": "/api/v1/Users/<USER>",
    "EmailApiBaseUrl": "http://email-server-ip/EmailApi",
    "SendEmail": "/api/SendEmail",
    "LDapServerIP": "ldap-server-ip",
    "LDapServerPort": "389"
  },
  "ApiSettings": {
    "WebhookSecret": "your-webhook-secret-key",
    "ExpectedSignatureHeader": "x-signature",
    "BearerToken": "your-bearer-token",
    "ClientId": "base64-encoded-client-id"
  },
  "Settings": {
    "Sender": "<EMAIL>"
  }
}
```

### 2. appsettings.Development.json
**Purpose**: Development environment overrides

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Debug",
      "Microsoft.AspNetCore": "Information"
    }
  },
  "ConnectionStrings": {
    "BID_CONFIRMATIONDB": "server=localhost\\SQLEXPRESS;Database=BID_CONFIRMATION_DEV;Integrated Security=true;TrustServerCertificate=True"
  },
  "JwtSettings": {
    "ExpiryMinutes": 60
  }
}
```

### 3. nlog.config
**Purpose**: Logging configuration

```xml
<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Trace"
      internalLogFile="C:\Logs\BIDCONFIRMATION\internallog.txt">
  <targets>
    <target name="logfile" xsi:type="File"
            fileName="C:\Logs\BIDCONFIRMATION\${shortdate}_logfile.txt"
            layout="${longdate} ${level:uppercase=true} ${message}"/>
  </targets>
  <rules>
    <logger name="*" minlevel="Debug" writeTo="logfile" />
  </rules>
</nlog>
```

### 4. launchSettings.json
**Purpose**: Development server configuration

```json
{
  "iisSettings": {
    "windowsAuthentication": false,
    "anonymousAuthentication": true,
    "iisExpress": {
      "applicationUrl": "http://localhost:13248",
      "sslPort": 44351
    }
  },
  "profiles": {
    "BIDConfirmation": {
      "commandName": "Project",
      "dotnetRunMessages": true,
      "launchBrowser": true,
      "launchUrl": "swagger",
      "applicationUrl": "https://localhost:7011;http://localhost:5056",
      "environmentVariables": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  }
}
```

## Environment-Specific Configuration

### Development Environment

**Database Configuration:**
```json
{
  "ConnectionStrings": {
    "BID_CONFIRMATIONDB": "server=localhost\\SQLEXPRESS;Database=BID_CONFIRMATION_DEV;Integrated Security=true;TrustServerCertificate=True"
  }
}
```

**Security Settings:**
- Extended JWT expiry (60 minutes)
- Detailed error messages enabled
- HTTPS redirection optional
- CORS allows all origins

**Logging:**
- Debug level logging
- Console and file output
- Detailed exception information

### Staging Environment

**Database Configuration:**
```json
{
  "ConnectionStrings": {
    "BID_CONFIRMATIONDB": "server=staging-db-server;Database=BID_CONFIRMATION_STAGING;uid=staging_user;password=staging_password;TrustServerCertificate=True"
  }
}
```

**Security Settings:**
- Production-like JWT expiry (30 minutes)
- Limited error details
- HTTPS enforced
- Restricted CORS origins

**Logging:**
- Information level logging
- File output only
- Performance monitoring

### Production Environment

**Database Configuration:**
```json
{
  "ConnectionStrings": {
    "BID_CONFIRMATIONDB": "server=prod-db-cluster;Database=BID_CONFIRMATION;uid=prod_user;password=secure_password;TrustServerCertificate=True"
  }
}
```

**Security Settings:**
- Short JWT expiry (5 minutes)
- Generic error messages
- HTTPS enforced
- Specific CORS origins
- Rate limiting enabled

**Logging:**
- Warning level and above
- Centralized logging
- Security event monitoring

## Security Configuration

### JWT Token Settings

| Setting | Development | Staging | Production |
|---------|-------------|---------|------------|
| ExpiryMinutes | 60 | 30 | 5 |
| SecretKey | Dev key | Staging key | Production key |
| Issuer | dev-app | staging-app | prod-app |
| Audience | dev-users | staging-users | prod-users |

### LDAP Configuration

```json
{
  "ExternalUrl": {
    "LDapServerIP": "ldap-server-ip",
    "LDapServerPort": "389"
  }
}
```

**Environment-Specific LDAP:**
- **Development**: Test LDAP server
- **Staging**: Staging LDAP server
- **Production**: Production Active Directory

### CORS Configuration

**Development:**
```csharp
options.AddPolicy("AllowAll", policy =>
{
    policy.AllowAnyOrigin()
          .AllowAnyHeader()
          .AllowAnyMethod();
});
```

**Production:**
```csharp
options.AddPolicy("AllowSpecificOrigins", policy =>
{
    policy.WithOrigins("https://your-frontend-domain.com")
          .AllowAnyHeader()
          .AllowAnyMethod()
          .AllowCredentials();
});
```

## Deployment Procedures

### Prerequisites

1. **.NET 6 Runtime** installed on target server
2. **SQL Server** accessible from application server
3. **IIS** or **Kestrel** web server configured
4. **SSL Certificate** for HTTPS
5. **Network Access** to LDAP and external services

### Deployment Steps

#### 1. Build and Publish

```bash
# Clean and restore
dotnet clean
dotnet restore

# Build for release
dotnet build --configuration Release

# Publish application
dotnet publish --configuration Release --output ./publish
```

#### 2. Database Deployment

```bash
# Generate migration script
dotnet ef migrations script --output migration.sql

# Apply migrations to target database
sqlcmd -S server -d database -i migration.sql
```

#### 3. Application Deployment

**IIS Deployment:**
1. Copy published files to IIS directory
2. Configure application pool (.NET 6)
3. Set up SSL certificate
4. Configure environment variables
5. Start application

**Docker Deployment:**
```dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:6.0
WORKDIR /app
COPY ./publish .
EXPOSE 80
EXPOSE 443
ENTRYPOINT ["dotnet", "BidConfirmationAPI.dll"]
```

#### 4. Configuration Updates

1. Update connection strings for target environment
2. Configure LDAP settings
3. Set JWT secret keys
4. Configure email service endpoints
5. Update CORS origins

#### 5. Post-Deployment Verification

1. **Health Check**: Verify application starts
2. **Database Connectivity**: Test database connections
3. **LDAP Authentication**: Verify LDAP connectivity
4. **API Endpoints**: Test key endpoints
5. **Logging**: Verify log file creation

### Environment Variables

**Sensitive Configuration:**
```bash
# Database connection
export BID_CONFIRMATION_DB_CONNECTION="server=***;Database=***;uid=***;password=***"

# JWT Secret
export JWT_SECRET_KEY="your-256-bit-secret-key"

# LDAP Configuration
export LDAP_SERVER_IP="ldap-server-ip"
export LDAP_SERVER_PORT="389"

# Email Service
export EMAIL_API_BASE_URL="http://email-server/EmailApi"
```

## Monitoring and Maintenance

### Application Monitoring

**Health Checks:**
- Database connectivity
- LDAP server availability
- External API accessibility
- Disk space monitoring

**Performance Metrics:**
- Response times
- Memory usage
- CPU utilization
- Database query performance

### Log Management

**Log Locations:**
- Application logs: `C:\Logs\BIDCONFIRMATION\`
- IIS logs: `C:\inetpub\logs\LogFiles\`
- Windows Event logs: Application and System

**Log Rotation:**
- Daily log files
- 30-day retention
- Automatic cleanup

**Log Monitoring:**
- Error rate alerts
- Performance degradation
- Security event monitoring

### Backup and Recovery

**Database Backup:**
- Full backup: Daily at 2:00 AM
- Transaction log backup: Every 15 minutes
- Backup verification: Weekly

**Application Backup:**
- Configuration files
- SSL certificates
- Application binaries

**Recovery Procedures:**
- Database restore procedures
- Application rollback process
- Configuration recovery

### Security Maintenance

**Regular Tasks:**
- SSL certificate renewal
- Security patch updates
- Dependency vulnerability scanning
- Access review and cleanup

**Security Monitoring:**
- Failed login attempts
- Unusual access patterns
- System intrusion detection
- Audit log review

## Troubleshooting

### Common Issues

**Database Connection Issues:**
1. Verify connection string
2. Check SQL Server service status
3. Validate network connectivity
4. Confirm user permissions

**LDAP Authentication Issues:**
1. Test LDAP server connectivity
2. Verify LDAP configuration
3. Check user credentials
4. Review firewall settings

**Performance Issues:**
1. Monitor database query performance
2. Check memory usage
3. Review log files for errors
4. Analyze response times

### Diagnostic Tools

**Application Diagnostics:**
- Swagger UI for API testing
- Application logs for error tracking
- Performance counters
- Health check endpoints

**Database Diagnostics:**
- SQL Server Management Studio
- Query execution plans
- Database performance monitor
- Index usage statistics

This configuration and deployment guide provides comprehensive coverage for managing the BID Confirmation Portal API across different environments.
