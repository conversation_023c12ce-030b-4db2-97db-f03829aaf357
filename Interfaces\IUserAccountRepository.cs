using BIDConfirmation.Model;
using BidConfirmationAPI.Model;

namespace BidConfirmationAPI.Interfaces
{
    public interface IUserAccountRepository
    {
        Task<UserAccountDetails?> GetDetailsByAccountNoAsync(string accountNo);
        Task<KachasiDetails?> GetKachasiDetailsAsync(string FormNumber);

        //Task<KachasiDetails?> GetKachasiDetailAsync(string PickupListID, string FormNumber);
    }
}