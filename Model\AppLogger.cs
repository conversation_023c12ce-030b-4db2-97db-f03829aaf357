﻿using NLog;

namespace BidConfirmationAPI.Models
{
    public class AppLogger
    {
        private static readonly NLog.ILogger _logger = LogManager.GetCurrentClassLogger();
        public static void LogInformation(string message)
        {
            Console.WriteLine($"{message}");
            _logger.Info(message);
        }
        public static void LogError(string message)
        {
            Console.WriteLine($"{message}");
            _logger.Error(message);
        }
    }
}
