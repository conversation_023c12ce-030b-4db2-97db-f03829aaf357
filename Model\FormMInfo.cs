﻿namespace BIDConfirmation.Model
{
    public class FormMInfo
    {
        public string formMNumber { get; set; }
        public string applicantName { get; set; }
        public string applicantAddress { get; set; }
        public string beneficiaryName { get; set; }
        public string applicantTin { get; set; }
        public string rcNumber { get; set; }
        public string applicantPhone { get; set; }
        public string countryOfSupply { get; set; }
        public string hsCode { get; set; }
        public string currency { get; set; }
        public decimal totalCFValue { get; set; }
        public DateTime formMRegistrationDate { get; set; }
        public string generalGoodsDescription { get; set; }
        public string paymentMode { get; set; }
        public string fundApplicationId { get; set; }
    }
}
