﻿using BIDConfirmation.Model;
using BidConfirmationAPI.Model;
using BidConfirmationAPI.Models;
using Microsoft.EntityFrameworkCore;
using Polly;
using System.Collections.Generic;
using System.Reflection.Emit;

namespace BidConfirmationAPI.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public ApplicationDbContext() { }

        // DbSets
        public DbSet<UserProfile> Users { get; set; }
        public DbSet<Tokens> Tokens { get; set; }
        public DbSet<UserActivity> UserActivities { get; set; }
        public DbSet<FailedLoginAttempt> FailedLoginAttempts { get; set; }
        public DbSet<AuditTrail> AuditTrails { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Document> Documents { get; set; }
        public DbSet<ApplicantDetails> ApplicantDetails { get; set; }

        public DbSet<DocumentDownload> DocumentDownloads { get; set; }
        public DbSet<RefreshToken> RefreshTokens { get; set; }


        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Relationships
            builder.Entity<ApplicantDetails>()
                   .HasMany(a => a.Documents)
                   .WithOne(d => d.Applicant)
                   .HasForeignKey(d => d.ApplicantDetailsId)
                   .OnDelete(DeleteBehavior.Cascade);

            // Indexes, constraints or default values can go here
            builder.Entity<Document>()
                   .Property(d => d.UploadedAt)
                   .HasDefaultValueSql("GETUTCDATE()");
        }
    }

}
